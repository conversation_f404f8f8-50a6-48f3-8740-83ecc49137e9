// YouTube utility functions

/**
 * Lấy YouTube thumbnail từ video URL
 * @param {string} videoUrl - URL video YouTube
 * @param {string} quality - Chất lượng thumbnail (maxresdefault, hqdefault, mqdefault, default)
 * @returns {string|null} - URL thumbnail hoặc null nếu không hợp lệ
 */
export const getYouTubeThumbnail = (videoUrl, quality = 'maxresdefault') => {
    // Lấy video ID từ YouTube URL
    const videoId = extractYouTubeVideoId(videoUrl);
    if (!videoId) return null;

    // Các tùy chọn chất lượng thumbnail:
    // maxresdefault (1280x720) - chất lượng cao nhất
    // hqdefault (480x360) - chất lượng cao
    // mqdefault (320x180) - chất lượng trung bình
    // default (120x90) - chất lượng thấp
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;
};

/**
 * Extract video ID từ YouTube URL
 * @param {string} url - URL video YouTube
 * @returns {string|null} - Video ID hoặc null nếu không hợp lệ
 */
export const extractYouTubeVideoId = (url) => {
    // Hỗ trợ nhiều định dạng YouTube URL:
    // https://www.youtube.com/watch?v=VIDEO_ID
    // https://youtu.be/VIDEO_ID
    // https://www.youtube.com/embed/VIDEO_ID
    // https://www.youtube.com/v/VIDEO_ID
    const regex = /(?:youtube\.com\/(?:[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
};

/**
 * Chuyển đổi YouTube URL thành embed URL
 * @param {string} videoUrl - URL video YouTube
 * @returns {string|null} - Embed URL hoặc null nếu không hợp lệ
 */
export const getYouTubeEmbedUrl = (videoUrl) => {
    const videoId = extractYouTubeVideoId(videoUrl);
    if (!videoId) return null;

    return `https://www.youtube.com/embed/${videoId}`;
};

/**
 * Kiểm tra xem URL có phải là YouTube URL hợp lệ không
 * @param {string} url - URL cần kiểm tra
 * @returns {boolean} - true nếu là YouTube URL hợp lệ
 */
export const isYouTubeUrl = (url) => extractYouTubeVideoId(url) !== null; 