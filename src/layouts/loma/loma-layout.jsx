'use client';

import { usePathname } from 'next/navigation';

import Box from '@mui/material/Box';

import { ClientOnly } from 'src/components/client-only';
import { ZaloWidget } from 'src/components/zalo-widget';
import { ChatwootWidget } from 'src/components/chatwoot';
import { ComparisonProvider } from 'src/components/comparison';

import { LomaHeader } from './header';
import { LomaFooter } from './footer';

// ----------------------------------------------------------------------

export function LomaLayout({ children, headerProps, footerProps }) {
  const pathname = usePathname();

  // Ẩn floating chatbot khi đang ở trang contact để tránh xung đột
  const shouldShowFloatingChatbot = pathname !== '/contact';

  return (
    <ComparisonProvider>
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          bgcolor: 'background.default',
          position: 'relative',
          overflow: 'hidden', // Giữ overflow hidden để tránh scroll ngang
        }}
      >

        <LomaHeader {...headerProps} />

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            pt: { xs: 8, md: 10 }, // Account for fixed header
            position: 'relative',
            zIndex: 1,
          }}
        >
          {children}
        </Box>

        <LomaFooter {...footerProps} />

        {/* AI Chatbot - Ẩn khi ở trang contact */}
        {/* {shouldShowFloatingChatbot && <ChatBot />} */}


        {/* Chatwoot Widget - Live Chat Support với ClientOnly wrapper */}
        <ClientOnly>
          <ChatwootWidget />
          <ZaloWidget />
        </ClientOnly>
      </Box>
    </ComparisonProvider>
  );
}
