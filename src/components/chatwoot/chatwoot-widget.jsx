'use client';

import { useState, useEffect } from 'react';

// ----------------------------------------------------------------------

export function ChatwootWidget() {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        // Đảm bảo component đã mount hoàn toàn
        setIsMounted(true);
    }, []);

    useEffect(() => {
        // Chỉ load Chatwoot khi component đã mount
        if (!isMounted) return undefined;

        // Thêm delay ngắn để đảm bảo hydration hoàn tất
        const timeoutId = setTimeout(() => {
            // Kiểm tra nếu đã có Chatwoot để tránh load multiple times
            if (window.chatwootSDK || window.$chatwoot) {
                return;
            }

            // Script injection chính xác theo Chatwoot dashboard
            (function (d, t) {
                var BASE_URL = "https://app.mooly.vn";
                var g = d.createElement(t), s = d.getElementsByTagName(t)[0];
                g.src = BASE_URL + "/packs/js/sdk.js";
                g.defer = true;
                g.async = true;
                s.parentNode.insertBefore(g, s);
                g.onload = function () {
                    window.chatwootSDK.run({
                        websiteToken: 'tXHWDVtRTijpdqbtsbKGzDea',
                        baseUrl: BASE_URL
                    })
                }
            })(document, "script");

            // Listen cho chatwoot:ready event để confirm widget đã load
            const handleChatwootReady = () => {
                console.log('✅ Chatwoot widget is ready!');
                if (window.$chatwoot) {
                    console.log('✅ Chatwoot $chatwoot object is available');
                }
            };

            const handleChatwootError = (error) => {
                console.error('❌ Chatwoot error:', error);
            };

            // Event listeners
            window.addEventListener('chatwoot:ready', handleChatwootReady);
            window.addEventListener('chatwoot:error', handleChatwootError);
        }, 1000);

        // Cleanup function khi component unmount
        return () => {
            clearTimeout(timeoutId);
        };
    }, [isMounted]);

    // Không render gì trong quá trình hydration
    if (!isMounted) {
        return null;
    }

    return null;
} 