'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Fab from '@mui/material/Fab';
import Tooltip from '@mui/material/Tooltip';
import { alpha, useTheme } from '@mui/material/styles';

import { getZaloPhone, openZaloWithFallback } from './utils';

// ----------------------------------------------------------------------

export function ZaloWidget() {
    const theme = useTheme();
    const [isHovered, setIsHovered] = useState(false);
    const [isVisible, setIsVisible] = useState(false);

    // Hiệu ứng fade in khi component mount
    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, 1500); // Delay 1.5s để tránh conflict vớ<PERSON>wootWidget

        return () => clearTimeout(timer);
    }, []);

    const handleZaloClick = () => {
        openZaloWithFallback();
    };

    if (!isVisible) return null;

    return (
        <Box
            sx={{
                position: 'fixed',
                bottom: { xs: 100, sm: 110, md: 120 }, // Responsive spacing trên ChatwootWidget
                right: { xs: 16, sm: 20, md: 24 },
                zIndex: 9999, // Cao hơn ChatwootWidget
                opacity: isVisible ? 1 : 0,
                transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
                transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
        >
            <Tooltip
                title={`Nhắn Zalo: ${getZaloPhone()}`}
                placement="left"
                arrow
                componentsProps={{
                    tooltip: {
                        sx: {
                            bgcolor: '#1a73e8',
                            fontSize: 14,
                            fontWeight: 600,
                            borderRadius: 2,
                            '& .MuiTooltip-arrow': {
                                color: '#1a73e8',
                            },
                        }
                    }
                }}
            >
                <Fab
                    onClick={handleZaloClick}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                    aria-label="Liên hệ qua Zalo"
                    sx={{
                        width: { xs: 56, sm: 60 },
                        height: { xs: 56, sm: 60 },
                        background: 'linear-gradient(135deg, #1a73e8 0%, #4285f4 50%, #34a853 100%)',
                        color: 'white',
                        boxShadow: '0 4px 20px rgba(26, 115, 232, 0.4)',
                        border: `3px solid ${alpha('#ffffff', 0.9)}`,
                        transform: isHovered ? 'scale(1.05) translateY(-3px)' : 'scale(1)',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&:hover': {
                            background: 'linear-gradient(135deg, #1557b0 0%, #3367d6 50%, #2d8a47 100%)',
                            boxShadow: '0 8px 30px rgba(26, 115, 232, 0.6)',
                        },
                        '&:active': {
                            transform: 'scale(0.98)',
                        },
                        // Animation hiệu ứng pulse với màu Zalo
                        '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: -8,
                            left: -8,
                            right: -8,
                            bottom: -8,
                            borderRadius: '50%',
                            background: 'linear-gradient(135deg, #1a73e8, #4285f4)',
                            opacity: 0.3,
                            zIndex: -1,
                            animation: 'zalo-pulse 2.5s infinite',
                        },
                        // Keyframes animation
                        '@keyframes zalo-pulse': {
                            '0%': {
                                transform: 'scale(1)',
                                opacity: 0.3,
                            },
                            '50%': {
                                transform: 'scale(1.2)',
                                opacity: 0.15,
                            },
                            '100%': {
                                transform: 'scale(1.4)',
                                opacity: 0,
                            },
                        },
                        // Hiệu ứng shine với màu gradient
                        '&::after': {
                            content: '""',
                            position: 'absolute',
                            top: '15%',
                            left: '15%',
                            width: '70%',
                            height: '70%',
                            borderRadius: '50%',
                            background: 'radial-gradient(circle at 30% 30%, rgba(255,255,255,0.8), rgba(255,255,255,0.2), transparent)',
                            opacity: isHovered ? 0.8 : 0.5,
                            transition: 'opacity 0.3s ease',
                            pointerEvents: 'none',
                        },
                    }}
                >
                    {/* Icon Zalo với styling cải tiến */}
                    <Box
                        sx={{
                            width: { xs: 32, sm: 36 },
                            height: { xs: 32, sm: 36 },
                            borderRadius: '50%',
                            background: 'rgba(255, 255, 255, 0.15)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            position: 'relative',
                            zIndex: 1,
                            transition: 'transform 0.3s ease',
                            transform: isHovered ? 'scale(1.1) rotate(5deg)' : 'scale(1)',
                        }}
                    >
                        <Box
                            component="img"
                            src="/assets/icons/zalo_icon.svg"
                            alt="Zalo"
                            sx={{
                                width: { xs: 20, sm: 24 },
                                height: { xs: 20, sm: 24 },
                                filter: 'brightness(0) invert(1) drop-shadow(0 1px 2px rgba(0,0,0,0.1))',
                                transition: 'all 0.3s ease',
                            }}
                        />
                    </Box>

                    {/* Badge "Z" nhỏ góc trên */}
                    <Box
                        sx={{
                            position: 'absolute',
                            top: -2,
                            right: -2,
                            width: 18,
                            height: 18,
                            borderRadius: '50%',
                            background: 'linear-gradient(135deg, #ff4444, #ff6b6b)',
                            border: '2px solid white',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: 10,
                            fontWeight: 700,
                            color: 'white',
                            animation: isHovered ? 'badge-bounce 0.6s ease' : 'none',
                            '@keyframes badge-bounce': {
                                '0%, 100%': { transform: 'scale(1)' },
                                '50%': { transform: 'scale(1.2)' },
                            },
                        }}
                    >
                        Z
                    </Box>
                </Fab>
            </Tooltip>
        </Box>
    );
} 