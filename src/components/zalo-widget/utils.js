import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

/**
 * Lấy URL Zalo từ config
 */
export const getZaloUrl = () => CONFIG.loma.contact.urls.zalo;

/**
 * Lấy số điện thoại Zalo để hiển thị
 */
export const getZaloPhone = () => CONFIG.loma.contact.phone;

/**
 * Mở Zalo trong tab mới
 */
export const openZaloChat = () => {
    window.open(getZaloUrl(), '_blank', 'noopener,noreferrer');
};

/**
 * Kiểm tra xem có thể sử dụng Zalo trên mobile không
 */
export const isMobileZaloSupported = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    return /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
};

/**
 * Tạo URL deep link cho Zalo app trên mobile
 */
export const getZaloDeepLink = () => {
    const phone = CONFIG.loma.contact.phoneInternational.replace('+', '');
    return `zalo://conversation?phone=${phone}`;
};

/**
 * Mở Zalo với fallback cho mobile
 */
export const openZaloWithFallback = () => {
    if (isMobileZaloSupported()) {
        // Thử mở app Zalo trước
        const deepLink = getZaloDeepLink();
        const fallbackUrl = getZaloUrl();

        // Tạo iframe ẩn để trigger deep link
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = deepLink;
        document.body.appendChild(iframe);

        // Fallback sau 1s nếu app không mở được
        setTimeout(() => {
            document.body.removeChild(iframe);
            window.open(fallbackUrl, '_blank', 'noopener,noreferrer');
        }, 1000);
    } else {
        // Desktop: mở web Zalo
        openZaloChat();
    }
}; 