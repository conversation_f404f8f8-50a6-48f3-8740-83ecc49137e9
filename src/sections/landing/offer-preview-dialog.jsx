'use client';

import { useState } from 'react';

import {
  <PERSON>,
  Card,
  Chip,
  Grid,
  <PERSON>ack,
  But<PERSON>,
  Dialog,
  Divider,
  useTheme,
  Typography,
  IconButton,
  DialogTitle,
  DialogContent,
  DialogActions,
  useMediaQuery,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

const PACKAGE_DETAILS = {
  free_sample: {
    images: [
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-01/3.jpg',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-01/1.jpg',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-04/tui-popo-phone-m.png'
    ],
    videoUrl: 'https://youtu.be/IbxBCTMG89Q', // Thay bằng video thật của bạn
    videoTitle: 'Xem chi tiết gói mẫu miễn phí',
    detailedDescription: 'Nhận 1 mẫu túi ngẫu nhiên MIỄN PHÍ để trải nghiệm chất lượng Loma.',
    highlights: [
      'Canvas/Vải đay linen (ngẫu nhiên)',
      'Tư vấn qua Zalo miễn phí',
      'Giao hàng 5-7 ngày',
    ],
    differentiators: [
      'Hoàn toàn MIỄN PHÍ - chỉ ship 30k',
      'Trải nghiệm thực tế chất lượng',
      'Không ràng buộc mua tiếp',
    ],
    paymentNote: 'Thanh toán online 30k phí ship → Giao túi mẫu miễn phí',
  },
  jute_collection: {
    images: [
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-05/tui-loma-3.png',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-05/tui-loma-4.png',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-01/3.jpg',
    ],
    videoUrl: 'https://youtu.be/MvjufuRKI-E', // Thay bằng video thật của bạn
    videoTitle: 'Khám phá bộ sưu tập 3 túi đa dạng chất liệu',
    detailedDescription: 'Bộ sưu tập 3 túi đa dạng chất liệu: 1 ngẫu nhiên + 1 vải đay + 1 canvas.',
    highlights: [
      '1 túi vải đay cao cấp in Lụa',
      '1 túi canvas cotton in Chuyển Nhiệt',
      'Tư vấn 1:1 qua Zalo',
      'Giao hàng 5-7 ngày',
    ],
    differentiators: [
      '3 túi sử dụng ngay hoặc làm mẫu',
      'So sánh 2 chất liệu và công nghệ in khác nhau',
      'Phù hợp đa dạng nhu cầu sử dụng',
    ],
    paymentNote: 'Thanh toán online 299k → Giao ngay bộ 3 túi đa dạng',
  },
  ultimate_collection: {
    images: [
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-07/tui-eco-dream3.png',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-07/tui-eco-dream.png',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-05/tui-loma-3.png',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-01/3.jpg',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-03/tui-dom-quai-da-bo-vien.png',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-03/tui-dom-quai-da-bo-vien-5.png',
      'https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-03/tui-dom-quai-da-bo-vien-4.png'
    ],
    videoUrl: 'https://youtu.be/UGrFa_MU5Us', // Thay bằng video thật của bạn
    videoTitle: 'Tìm hiểu bộ sưu tập hoàn chỉnh 5 túi',
    detailedDescription: 'Bộ sưu tập hoàn chỉnh 5 túi đa dạng chất liệu để so sánh trực tiếp.',
    highlights: [
      '3 phương pháp in: Lụa + Chuyển Nhiệt + Pet',
      'Tư vấn 1:1 + Video call',
      'Giao hàng 5-7 ngày',
    ],
    differentiators: [
      'Bộ sưu tập hoàn chỉnh nhất - 5 túi',
      'So sánh tất cả chất liệu và công nghệ in',
      'Tiết kiệm thời gian quyết định',
    ],
    paymentNote: 'Thanh toán online 399k → Giao ngay bộ 5 túi hoàn hảo',
  },
};

// ----------------------------------------------------------------------

export function OfferPreviewDialog({ open, onClose, packageData }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showVideo, setShowVideo] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (!packageData) return null;

  const details = PACKAGE_DETAILS[packageData.name];
  const images = details?.images || [];

  const formatPrice = (price) => {
    if (price === 30000) {
      return 'Chỉ 30k ship';
    }
    if (price < 1000) {
      return `${price}k`;
    }
    return `${Math.floor(price / 1000)}k`;
  };

  const formatOriginalPrice = (originalPrice) => {
    if (!originalPrice) return null;
    if (originalPrice < 1000) {
      return `${originalPrice}k`;
    }
    return `${Math.floor(originalPrice / 1000)}k`;
  };

  // Helper function để lấy YouTube video ID từ URL
  const getYouTubeVideoId = (url) => {
    if (!url) return null;
    const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[7].length === 11) ? match[7] : null;
  };

  // Tạo YouTube embed URL
  const getYouTubeEmbedUrl = (videoId) =>
    `https://www.youtube.com/embed/${videoId}?autoplay=0&rel=0&modestbranding=1&showinfo=0`;

  const handleNextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const handlePrevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleOrderClick = () => {
    // Redirect to checkout page with package name
    window.open(`/mua-tui-mau/checkout?package=${packageData.name}`, '_blank');
    onClose();
  };

  const handleVideoToggle = (show) => {
    setShowVideo(show);
    if (show) {
      setVideoLoaded(false);
      // Analytics tracking có thể thêm ở đây
      // gtag('event', 'video_play', { package_name: packageData.name });
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
      slotProps={{
        paper: {
          sx: {
            overflow: 'hidden',
            maxHeight: isMobile ? '100vh' : '95vh',
            display: 'flex',
            flexDirection: 'column',
            m: isMobile ? 0 : 2,
            borderRadius: isMobile ? 0 : 2,
          },
        },
      }}
    >
      {/* DialogTitle */}
      <DialogTitle
        sx={{
          p: isMobile ? 2 : 3,
          bgcolor: packageData.color_hex,
          color: 'white',
          position: 'relative',
        }}
      >
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: isMobile ? 8 : 16,
            right: isMobile ? 8 : 16,
            color: 'white',
            bgcolor: 'rgba(255, 255, 255, 0.1)',
            size: isMobile ? 'small' : 'medium',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.2)',
            },
          }}
        >
          <Iconify icon="solar:close-circle-bold" width={isMobile ? 20 : 24} />
        </IconButton>

        <Stack
          direction={isSmallMobile ? "column" : "row"}
          alignItems={isSmallMobile ? "flex-start" : "center"}
          spacing={isSmallMobile ? 1 : 2}
          sx={{ mb: isMobile ? 1.5 : 2, pr: 6 }}
        >
          <Typography
            variant={isMobile ? "h4" : "h3"}
            component="h2"
            fontWeight={700}
            sx={{ lineHeight: 1.2, color: 'inherit' }}
          >
            {packageData.title}
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {packageData.is_popular && (
              <Chip
                label="PHỔ BIẾN NHẤT"
                size={isMobile ? "small" : "medium"}
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontWeight: 600,
                  fontSize: isMobile ? '0.7rem' : '0.8rem',
                }}
              />
            )}
            {packageData.name === 'free_sample' && (
              <Chip
                label="MIỄN PHÍ"
                size={isMobile ? "small" : "medium"}
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontWeight: 600,
                  fontSize: isMobile ? '0.7rem' : '0.8rem',
                }}
              />
            )}
          </Stack>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={2} flexWrap="wrap">
          {packageData.original_price && (
            <Typography
              variant={isMobile ? "body2" : "body1"}
              sx={{ textDecoration: 'line-through', opacity: 0.7, color: 'inherit' }}
            >
              {formatOriginalPrice(packageData.original_price)}
            </Typography>
          )}
          <Typography
            variant={isMobile ? "h3" : "h2"}
            fontWeight={800}
            sx={{ color: 'inherit' }}
          >
            {formatPrice(packageData.price)}
          </Typography>
          {packageData.name === 'free_sample' && (
            <Typography
              variant={isMobile ? "body2" : "body1"}
              fontWeight={600}
              sx={{ color: 'inherit' }}
            >
              (Mẫu túi MIỄN PHÍ)
            </Typography>
          )}
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ p: 0, flex: 1, overflow: 'auto' }}>
        <Box sx={{ p: isMobile ? 2 : 4, pb: 1 }}>
          <Grid container spacing={isMobile ? 2 : 4}>
            {/* Media Section - Images & Video */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Box sx={{ position: 'relative' }}>
                {/* Media Toggle Buttons */}
                <Stack
                  direction="row"
                  spacing={1}
                  sx={{ mb: isMobile ? 1.5 : 2 }}
                >
                  <Button
                    variant={!showVideo ? "contained" : "outlined"}
                    size={isMobile ? "small" : "medium"}
                    onClick={() => handleVideoToggle(false)}
                    startIcon={<Iconify icon="solar:gallery-bold" width={16} />}
                    sx={{
                      bgcolor: !showVideo ? packageData.color_hex : 'transparent',
                      borderColor: packageData.color_hex,
                      color: !showVideo ? 'white' : packageData.color_hex,
                      '&:hover': {
                        bgcolor: !showVideo ? packageData.color_hex : `${packageData.color_hex}10`,
                        filter: !showVideo ? 'brightness(0.9)' : 'none',
                      },
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      px: isMobile ? 1.5 : 2,
                    }}
                  >
                    Hình ảnh
                  </Button>
                  {details?.videoUrl && (
                    <Button
                      variant={showVideo ? "contained" : "outlined"}
                      size={isMobile ? "small" : "medium"}
                      onClick={() => handleVideoToggle(true)}
                      startIcon={<Iconify icon="solar:play-circle-bold" width={16} />}
                      sx={{
                        bgcolor: showVideo ? packageData.color_hex : 'transparent',
                        borderColor: packageData.color_hex,
                        color: showVideo ? 'white' : packageData.color_hex,
                        '&:hover': {
                          bgcolor: showVideo ? packageData.color_hex : `${packageData.color_hex}10`,
                          filter: showVideo ? 'brightness(0.9)' : 'none',
                        },
                        fontSize: isMobile ? '0.75rem' : '0.875rem',
                        px: isMobile ? 1.5 : 2,
                      }}
                    >
                      Video
                    </Button>
                  )}
                </Stack>

                {/* Media Content */}
                <Card
                  sx={{
                    overflow: 'hidden',
                    aspectRatio: isMobile ? '16/12' : '4/3',
                    position: 'relative',
                  }}
                >
                  {showVideo && details?.videoUrl ? (
                    // Video Player
                    <Box
                      sx={{
                        width: '100%',
                        height: '100%',
                        position: 'relative',
                        bgcolor: '#000',
                      }}
                    >
                      {(() => {
                        const videoId = getYouTubeVideoId(details.videoUrl);
                        return videoId ? (
                          <>
                            {!videoLoaded && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  width: '100%',
                                  height: '100%',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  bgcolor: 'rgba(0, 0, 0, 0.8)',
                                  zIndex: 1,
                                }}
                              >
                                <Stack alignItems="center" spacing={2}>
                                  <Box
                                    sx={{
                                      width: 40,
                                      height: 40,
                                      border: `3px solid ${packageData.color_hex}30`,
                                      borderTop: `3px solid ${packageData.color_hex}`,
                                      borderRadius: '50%',
                                      animation: 'spin 1s linear infinite',
                                      '@keyframes spin': {
                                        '0%': { transform: 'rotate(0deg)' },
                                        '100%': { transform: 'rotate(360deg)' },
                                      },
                                    }}
                                  />
                                  <Typography variant="body2" color="white">
                                    Đang tải video...
                                  </Typography>
                                </Stack>
                              </Box>
                            )}
                            <Box
                              component="iframe"
                              src={getYouTubeEmbedUrl(videoId)}
                              title={details.videoTitle || `Video ${packageData.title}`}
                              onLoad={() => setVideoLoaded(true)}
                              sx={{
                                width: '100%',
                                height: '100%',
                                border: 'none',
                              }}
                              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                              allowFullScreen
                            />
                          </>
                        ) : (
                          <Box
                            sx={{
                              width: '100%',
                              height: '100%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              flexDirection: 'column',
                              color: 'white',
                              gap: 2,
                            }}
                          >
                            <Iconify icon="solar:video-frame-play-vertical-bold" width={48} />
                            <Typography variant="body2" textAlign="center">
                              Video đang cập nhật
                            </Typography>
                          </Box>
                        );
                      })()}
                    </Box>
                  ) : (
                    // Image Gallery
                    <>
                      {images.length > 0 ? (
                        <Box
                          component="img"
                          src={images[currentImageIndex]}
                          alt={`${packageData.title} - Hình ${currentImageIndex + 1}`}
                          sx={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                          }}
                        />
                      ) : (
                        <Box
                          sx={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            bgcolor: 'grey.100',
                            color: 'text.secondary',
                          }}
                        >
                          <Typography variant={isMobile ? "body1" : "h6"}>Hình ảnh đang cập nhật</Typography>
                        </Box>
                      )}

                      {/* Image Navigation */}
                      {images.length > 1 && !showVideo && (
                        <>
                          <IconButton
                            onClick={handlePrevImage}
                            size={isMobile ? "small" : "medium"}
                            sx={{
                              position: 'absolute',
                              left: isMobile ? 8 : 16,
                              top: '50%',
                              transform: 'translateY(-50%)',
                              bgcolor: 'rgba(0, 0, 0, 0.5)',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'rgba(0, 0, 0, 0.7)',
                              },
                            }}
                          >
                            <Iconify icon="solar:arrow-left-bold" width={isMobile ? 16 : 20} />
                          </IconButton>

                          <IconButton
                            onClick={handleNextImage}
                            size={isMobile ? "small" : "medium"}
                            sx={{
                              position: 'absolute',
                              right: isMobile ? 8 : 16,
                              top: '50%',
                              transform: 'translateY(-50%)',
                              bgcolor: 'rgba(0, 0, 0, 0.5)',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'rgba(0, 0, 0, 0.7)',
                              },
                            }}
                          >
                            <Iconify icon="solar:arrow-right-bold" width={isMobile ? 16 : 20} />
                          </IconButton>
                        </>
                      )}
                    </>
                  )}
                </Card>

                {/* Image Dots */}
                {images.length > 1 && !showVideo && (
                  <Stack
                    direction="row"
                    justifyContent="center"
                    spacing={0.5}
                    sx={{ mt: isMobile ? 1 : 2 }}
                  >
                    {images.map((_, index) => (
                      <Box
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        sx={{
                          width: isMobile ? 6 : 8,
                          height: isMobile ? 6 : 8,
                          borderRadius: '50%',
                          bgcolor: index === currentImageIndex ? packageData.color_hex : 'grey.300',
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                        }}
                      />
                    ))}
                  </Stack>
                )}

                {/* Video Info */}
                {showVideo && details?.videoTitle && (
                  <Box
                    sx={{
                      mt: isMobile ? 1 : 2,
                      p: isMobile ? 1.5 : 2,
                      bgcolor: `${packageData.color_hex}10`,
                      borderRadius: 2,
                      border: `1px solid ${packageData.color_hex}30`,
                    }}
                  >
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Iconify
                        icon="solar:play-circle-bold"
                        width={20}
                        color={packageData.color_hex}
                      />
                      <Typography
                        variant="body2"
                        fontWeight={600}
                        color={packageData.color_hex}
                        sx={{ fontSize: isMobile ? '0.8rem' : '0.875rem' }}
                      >
                        {details.videoTitle}
                      </Typography>
                    </Stack>
                  </Box>
                )}
              </Box>
            </Grid>

            {/* Content */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Stack spacing={isMobile ? 2 : 3}>
                {/* Description */}
                <Box>
                  <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight={600} sx={{ mb: isMobile ? 1 : 2 }}>
                    Mô tả chi tiết
                  </Typography>
                  <Typography
                    variant={isMobile ? "body2" : "body2"}
                    color="text.secondary"
                    lineHeight={isMobile ? 1.5 : 1.6}
                    sx={{ fontSize: isMobile ? '0.85rem' : '0.875rem' }}
                  >
                    {details?.detailedDescription || packageData.description}
                  </Typography>

                  {/* Video CTA */}
                  {details?.videoUrl && !showVideo && (
                    <Box
                      sx={{
                        mt: isMobile ? 1.5 : 2,
                        p: isMobile ? 1.5 : 2,
                        bgcolor: `${packageData.color_hex}08`,
                        borderRadius: 2,
                        border: `1px dashed ${packageData.color_hex}40`,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          bgcolor: `${packageData.color_hex}15`,
                          transform: 'translateY(-2px)',
                          boxShadow: `0 4px 20px ${packageData.color_hex}20`,
                        },
                      }}
                      onClick={() => handleVideoToggle(true)}
                    >
                      <Stack direction="row" alignItems="center" spacing={1.5}>
                        <Box
                          sx={{
                            width: 40,
                            height: 40,
                            borderRadius: '50%',
                            bgcolor: packageData.color_hex,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            animation: 'pulse 2s infinite',
                            '@keyframes pulse': {
                              '0%': {
                                transform: 'scale(1)',
                                boxShadow: `0 0 0 0 ${packageData.color_hex}40`,
                              },
                              '70%': {
                                transform: 'scale(1.05)',
                                boxShadow: `0 0 0 10px ${packageData.color_hex}00`,
                              },
                              '100%': {
                                transform: 'scale(1)',
                                boxShadow: `0 0 0 0 ${packageData.color_hex}00`,
                              },
                            },
                          }}
                        >
                          <Iconify icon="solar:play-bold" width={20} color="white" />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="body1"
                            fontWeight={600}
                            color={packageData.color_hex}
                            sx={{ fontSize: isMobile ? '0.9rem' : '1rem' }}
                          >
                            🎥 Xem video chi tiết sản phẩm
                          </Typography>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ fontSize: isMobile ? '0.8rem' : '0.875rem' }}
                          >
                            Hiểu rõ hơn về chất lượng và quy trình sản xuất
                          </Typography>
                        </Box>
                        <Iconify
                          icon="solar:arrow-right-bold"
                          width={16}
                          color={packageData.color_hex}
                        />
                      </Stack>
                    </Box>
                  )}
                </Box>

                <Divider />

                {/* Visual Bag Count */}
                <Box sx={{
                  textAlign: 'center',
                  py: isMobile ? 1.5 : 2,
                  bgcolor: 'grey.50',
                  borderRadius: 2
                }}>
                  <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight={600} sx={{ mb: isMobile ? 1.5 : 2 }}>
                    BẠN SẼ NHẬN ĐƯỢC
                  </Typography>

                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'flex-end',
                    gap: isMobile ? 1 : 1.5,
                    mb: isMobile ? 1.5 : 2,
                    flexWrap: 'wrap'
                  }}>
                    {Array.from({
                      length:
                        packageData.name === 'free_sample' ? 1 :
                          packageData.name === 'ultimate_collection' ? 5 : 3
                    }).map((_, index) => {
                      // Xác định loại túi cho từng index
                      const getBagType = (packageName, bagIndex) => {
                        if (packageName === 'free_sample') {
                          return 'random';
                        }
                        if (packageName === 'jute_collection') {
                          if (bagIndex === 0) return 'random';
                          if (bagIndex === 1) return 'jute';
                          return 'canvas';
                        }
                        if (packageName === 'ultimate_collection') {
                          if (bagIndex === 0) return 'random';
                          if (bagIndex <= 2) return 'jute';
                          return 'canvas';
                        }
                        return 'random';
                      };

                      const bagType = getBagType(packageData.name, index);
                      const bagLabels = {
                        random: 'Ngẫu nhiên',
                        jute: 'Túi đay',
                        canvas: 'Túi canvas'
                      };

                      return (
                        <Box
                          key={index}
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            gap: 0.5,
                          }}
                        >
                          <Box
                            sx={{
                              width: isMobile
                                ? (packageData.name === 'ultimate_collection' ? 36 : 44)
                                : (packageData.name === 'ultimate_collection' ? 48 : 56),
                              height: isMobile
                                ? (packageData.name === 'ultimate_collection' ? 36 : 44)
                                : (packageData.name === 'ultimate_collection' ? 48 : 56),
                              borderRadius: 2,
                              bgcolor: packageData.color_hex,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              position: 'relative',
                              boxShadow: 2,
                            }}
                          >
                            <Iconify
                              icon="solar:bag-4-bold"
                              width={isMobile ? 20 : 24}
                              color="white"
                            />
                          </Box>
                          <Chip
                            label={bagLabels[bagType]}
                            size="small"
                            sx={{
                              fontSize: isMobile ? '0.55rem' : '0.6rem',
                              height: isMobile ? 14 : 16,
                              bgcolor: bagType === 'random' ? '#00b894' :
                                bagType === 'jute' ? '#8b4513' : '#4a90e2',
                              color: 'white',
                              fontWeight: 600,
                              minWidth: isMobile ? 60 : 70,
                              '& .MuiChip-label': { px: isMobile ? 0.5 : 1 },
                            }}
                          />
                        </Box>
                      );
                    })}
                  </Box>

                  <Typography variant={isMobile ? "h6" : "h5"} fontWeight={700} color={packageData.color_hex}>
                    {packageData.name === 'free_sample' ? '1 TÚI (MIỄN PHÍ)' :
                      packageData.name === 'ultimate_collection' ? '5 TÚI (SỞ HỮU NGAY)' : '3 TÚI (SỞ HỮU NGAY)'}
                  </Typography>

                  {packageData.name !== 'free_sample' && (
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        mt: 0.5,
                        fontSize: isMobile ? '0.8rem' : '0.875rem',
                        px: 1
                      }}
                    >
                      {packageData.name === 'ultimate_collection'
                        ? '1 túi ngẫu nhiên + 2 túi vải đay + 2 túi canvas'
                        : '1 túi ngẫu nhiên + 1 túi vải đay + 1 túi canvas'
                      }
                    </Typography>
                  )}
                </Box>

                <Divider />

                {/* Highlights */}
                <Box>
                  <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight={600} sx={{ mb: isMobile ? 1 : 2 }}>
                    Bạn sẽ nhận được
                  </Typography>
                  <Stack spacing={isMobile ? 1 : 1.5}>
                    {(details?.highlights || packageData.features || []).map((highlight, index) => (
                      <Typography
                        key={index}
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          fontSize: isMobile ? '0.8rem' : '0.875rem',
                          lineHeight: isMobile ? 1.4 : 1.5,
                          '&::before': {
                            content: '"✓"',
                            color: '#00b894',
                            fontWeight: 'bold',
                            marginRight: '8px'
                          }
                        }}
                      >
                        {highlight}
                      </Typography>
                    ))}
                  </Stack>
                </Box>

                <Divider />

                {/* Payment Information */}
                <Box
                  sx={{
                    p: isMobile ? 2 : 3,
                    bgcolor: '#fff3e0',
                    border: '2px solid #ff9800',
                    borderRadius: 2,
                    textAlign: 'center',
                  }}
                >
                  <Typography
                    variant={isMobile ? "subtitle1" : "h6"}
                    fontWeight={700}
                    color="#e65100"
                    sx={{ mb: isMobile ? 1 : 1.5 }}
                  >
                    💳 THANH TOÁN ONLINE - GIAO HÀNG NGAY
                  </Typography>
                  <Typography
                    variant="body1"
                    fontWeight={600}
                    color="#ef6c00"
                    sx={{ fontSize: isMobile ? '0.9rem' : '1rem' }}
                  >
                    {details?.paymentNote}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="#bf360c"
                    sx={{
                      mt: 1,
                      fontSize: isMobile ? '0.8rem' : '0.875rem',
                      fontStyle: 'italic'
                    }}
                  >
                    ⚠️ Cần hoàn tất thanh toán online trước khi chúng tôi giao túi
                  </Typography>
                </Box>

                <Divider />

                {/* Differentiators */}
                {details?.differentiators && (
                  <Box>
                    <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight={600} sx={{ mb: isMobile ? 1 : 2 }}>
                      Ưu điểm nổi bật
                    </Typography>
                    <Stack spacing={isMobile ? 1 : 1.5}>
                      {details.differentiators.map((point, index) => (
                        <Typography
                          key={index}
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            fontSize: isMobile ? '0.8rem' : '0.875rem',
                            lineHeight: isMobile ? 1.4 : 1.5,
                            '&::before': {
                              content: '"⭐"',
                              color: '#ffc107',
                              marginRight: '8px'
                            }
                          }}
                        >
                          {point}
                        </Typography>
                      ))}
                    </Stack>
                  </Box>
                )}
              </Stack>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      {/* Fixed DialogActions */}
      <DialogActions
        sx={{
          p: isMobile ? 2 : 3,
          pt: isMobile ? 1.5 : 2,
          borderTop: '1px solid',
          borderColor: 'divider',
          bgcolor: 'background.paper',
          position: 'sticky',
          bottom: 0,
          zIndex: 1,
        }}
      >
        <Stack spacing={isMobile ? 1.5 : 2} sx={{ width: '100%' }}>
          <Button
            variant="contained"
            size="large"
            fullWidth
            onClick={handleOrderClick}
            sx={{
              py: isMobile ? 1.5 : 2,
              bgcolor: packageData.color_hex,
              borderRadius: isMobile ? 2 : 3,
              fontWeight: 700,
              fontSize: isMobile ? '1rem' : '1.2rem',
              boxShadow: `0 8px 25px ${packageData.color_hex}30`,
              '&:hover': {
                bgcolor: packageData.color_hex,
                filter: 'brightness(0.9)',
                transform: 'translateY(-2px)',
                boxShadow: `0 12px 35px ${packageData.color_hex}40`,
              },
              transition: 'all 0.3s ease',
            }}
          >
            {packageData.name === 'free_sample'
              ? '💳 Thanh toán 30k - Nhận túi miễn phí'
              : `💳 Thanh toán ${formatPrice(packageData.price)} - Nhận ngay`
            }
          </Button>

          {/* Payment Process Info */}
          <Box
            sx={{
              textAlign: 'center',
              bgcolor: '#f3e5f5',
              p: isMobile ? 1.5 : 2,
              borderRadius: 2,
              border: '1px solid #e1bee7'
            }}
          >
            <Typography
              variant="body2"
              fontWeight={600}
              color="#7b1fa2"
              sx={{
                mb: 1,
                fontSize: isMobile ? '0.8rem' : '0.875rem'
              }}
            >
              🔄 QUY TRÌNH THANH TOÁN & GIAO HÀNG
            </Typography>
            <Stack
              direction={isMobile ? "column" : "row"}
              justifyContent="center"
              spacing={isMobile ? 0.5 : 2}
              flexWrap="wrap"
            >
              <Typography
                variant="caption"
                color="#4a148c"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: isMobile ? 'center' : 'flex-start',
                  fontSize: isMobile ? '0.7rem' : '0.75rem',
                  fontWeight: 500
                }}
              >
                1️⃣ Thanh toán online ngay
              </Typography>
              <Typography
                variant="caption"
                color="#4a148c"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: isMobile ? 'center' : 'flex-start',
                  fontSize: isMobile ? '0.7rem' : '0.75rem',
                  fontWeight: 500
                }}
              >
                2️⃣ Chúng tôi chuẩn bị túi
              </Typography>
              <Typography
                variant="caption"
                color="#4a148c"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: isMobile ? 'center' : 'flex-start',
                  fontSize: isMobile ? '0.7rem' : '0.75rem',
                  fontWeight: 500
                }}
              >
                3️⃣ Giao hàng đến tay bạn
              </Typography>
            </Stack>
          </Box>
        </Stack>
      </DialogActions>
    </Dialog>
  );
}
