'use client';

import { usePathname } from 'next/navigation';

import Box from '@mui/material/Box';

import { ClientOnly } from 'src/components/client-only';
import { ZaloWidget } from 'src/components/zalo-widget';
import { ChatwootWidget } from 'src/components/chatwoot';

import { LandingHeader } from './landing-header';
import { LandingNotification } from './landing-notification';

// ----------------------------------------------------------------------

export function LandingLayout({ children }) {
  const pathname = usePathname();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: '#faf8f5', // Matching HTML background
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Notification Banner - Above everything */}
      <LandingNotification />

      <LandingHeader />

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          position: 'relative',
          zIndex: 1,
          pt: {
            xs: '100px', // Mobile: notification (48px) + mobile notice (48px) + header (96px)
            md: '128px', // Desktop: notification (48px) + header (80px)
          },
        }}
      >
        {children}
      </Box>
      <ClientOnly>
        <ChatwootWidget />
        <ZaloWidget />
      </ClientOnly>

      {/* Mobile Notice */}
      {/* <LandingMobileNotice /> */}

      {/* Floating CTA */}
      {/* <LandingFloatingCTA /> */}
    </Box>
  );
}
