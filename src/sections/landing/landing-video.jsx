'use client';

import { useState } from 'react';
import { m } from 'framer-motion';

import Box from '@mui/material/Box';
import Fade from '@mui/material/Fade';
import Modal from '@mui/material/Modal';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';

import { getYouTubeThumbnail, getYouTubeEmbedUrl } from 'src/utils/youtube';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

export function LandingVideo() {
  const [open, setOpen] = useState(false);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  // Video URL gốc - thay thế bằng video YouTube thực tế của bạn
  const originalVideoUrl = 'https://www.youtube.com/watch?v=IbxBCTMG89Q';

  // Tự động tạo embed URL và lấy thumbnail từ YouTube
  const videoUrl = getYouTubeEmbedUrl(originalVideoUrl);
  const videoThumbnail = getYouTubeThumbnail(originalVideoUrl) || 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=800&h=450&fit=crop';

  return (
    <Box
      sx={{
        py: { xs: 8, md: 12 },
        bgcolor: 'white',
      }}
    >
      <Container>
        <MotionViewport>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <m.div variants={varFade('inUp')}>
              <Typography
                variant="h2"
                fontWeight={700}
                color="#2d1810"
                sx={{ mb: 2 }}
              >
                Xem video bộ mẫu túi vải
              </Typography>
            </m.div>

            <m.div variants={varFade('inUp')}>
              <Typography
                variant="h5"
                color="#6b4423"
                sx={{ maxWidth: 700, mx: 'auto' }}
              >
                Video thực tế về chất lượng và quy trình sản xuất túi vải tại xưởng LOMA
              </Typography>
            </m.div>
          </Box>

          {/* Video Thumbnail */}
          <m.div variants={varFade('inUp')}>
            <Box
              sx={{
                position: 'relative',
                maxWidth: 800,
                mx: 'auto',
                borderRadius: 3,
                overflow: 'hidden',
                cursor: 'pointer',
                boxShadow: '0 15px 35px rgba(0, 0, 0, 0.15)',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 25px 50px rgba(0, 0, 0, 0.2)',
                },
                transition: 'all 0.3s ease',
              }}
              onClick={handleOpen}
            >
              {/* Video Thumbnail */}
              <Box
                component="img"
                src={videoThumbnail}
                alt="Video bộ mẫu túi vải LOMA"
                sx={{
                  width: '100%',
                  height: { xs: 250, md: 450 },
                  objectFit: 'cover',
                  display: 'block',
                }}
              />

              {/* Play Button Overlay */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'rgba(0, 0, 0, 0.3)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    bgcolor: 'rgba(0, 0, 0, 0.5)',
                  },
                }}
              >
                <IconButton
                  sx={{
                    width: 80,
                    height: 80,
                    bgcolor: 'rgba(255, 107, 53, 0.9)',
                    color: 'white',
                    '&:hover': {
                      bgcolor: '#ff6b35',
                      transform: 'scale(1.1)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  <Iconify icon="solar:play-bold" width={40} />
                </IconButton>
              </Box>

              {/* Video Duration Badge */}
              <Box
                sx={{
                  position: 'absolute',
                  bottom: 16,
                  right: 16,
                  bgcolor: 'rgba(0, 0, 0, 0.8)',
                  color: 'white',
                  px: 2,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '0.9rem',
                  fontWeight: 600,
                }}
              >
                3:45
              </Box>
            </Box>
          </m.div>

          {/* Video Benefits */}
          <m.div variants={varFade('inUp')}>
            <Box
              sx={{
                mt: 6,
                p: 4,
                borderRadius: 3,
                background: 'linear-gradient(135deg, #faf8f5 0%, #f5f1eb 100%)',
                textAlign: 'center',
              }}
            >
              <Typography variant="h5" fontWeight={600} color="#2d1810" sx={{ mb: 3 }}>
                🎬 Trong video này bạn sẽ thấy:
              </Typography>

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' },
                  gap: 2,
                  textAlign: 'left',
                }}
              >
                {[
                  '✅ Quy trình sản xuất túi vải tại xưởng',
                  '✅ So sánh chất lượng các loại vải thực tế',
                  '✅ Các phương pháp in logo khác nhau',
                  '✅ Bảng mẫu vải đầy đủ và chi tiết',
                ].map((item, index) => (
                  <Typography
                    key={index}
                    variant="body1"
                    color="#6b4423"
                    sx={{ fontWeight: 500 }}
                  >
                    {item}
                  </Typography>
                ))}
              </Box>
            </Box>
          </m.div>
        </MotionViewport>
      </Container>

      {/* Video Modal */}
      <Modal
        open={open}
        onClose={handleClose}
        closeAfterTransition
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2,
        }}
      >
        <Fade in={open}>
          <Box
            sx={{
              position: 'relative',
              width: '100%',
              maxWidth: 900,
              bgcolor: 'black',
              borderRadius: 2,
              overflow: 'hidden',
              outline: 'none',
            }}
          >
            {/* Close Button */}
            <IconButton
              onClick={handleClose}
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                zIndex: 1,
                bgcolor: 'rgba(0, 0, 0, 0.5)',
                color: 'white',
                '&:hover': {
                  bgcolor: 'rgba(0, 0, 0, 0.7)',
                },
              }}
            >
              <Iconify icon="solar:close-circle-bold" width={24} />
            </IconButton>

            {/* Video Iframe */}
            <Box
              component="iframe"
              src={videoUrl}
              sx={{
                width: '100%',
                height: { xs: 250, md: 500 },
                border: 'none',
              }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </Box>
        </Fade>
      </Modal>
    </Box>
  );
}
