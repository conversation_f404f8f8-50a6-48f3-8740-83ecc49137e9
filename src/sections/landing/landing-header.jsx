'use client';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';

// ----------------------------------------------------------------------

export function LandingHeader() {
  const handleOrderClick = () => {
    const packageElement = document.getElementById('package-options');
    if (packageElement) {
      packageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const handlePackagesClick = () => {
    const packageElement = document.getElementById('package-options');
    if (packageElement) {
      packageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const handleGalleryClick = () => {
    const galleryElement = document.getElementById('gallery');
    if (galleryElement) {
      galleryElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const handleMockupClick = () => {
    const mockupElement = document.getElementById('mockup');
    if (mockupElement) {
      mockupElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <Box
      component="header"
      sx={{
        position: 'fixed',
        top: {
          xs: '64px', // Mobile: notification (48px) + mobile notice (48px)
          sm: '40px'  // Desktop: chỉ có notification (48px)
        },
        left: 0,
        right: 0,
        zIndex: 1100, // Dưới notification (1200) và mobile notice (1150)
        bgcolor: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        py: 1.5,
        boxShadow: '0 2px 20px rgba(0, 0, 0, 0.1)',
        borderBottom: '1px solid rgba(255, 107, 53, 0.1)',
        // CSS Isolation để tránh xung đột
        isolation: 'isolate',
        contain: 'layout style',
      }}
    >
      <Container>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box
            component="img"
            src="https://loma.vn/logo/logo-full.png"
            alt="Loma Bags"
            sx={{
              height: 45,
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'scale(1.05)',
              },
            }}
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          />

          <Button
            variant="contained"
            size="large"
            onClick={handleOrderClick}
            sx={{
              background: 'linear-gradient(135deg, #ff6b35 0%, #f7941d 100%)',
              color: 'white',
              px: 3,
              py: 1.5,
              borderRadius: '25px',
              fontWeight: 600,
              boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 20px rgba(255, 107, 53, 0.4)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            Nhận Miễn Phí 1 Túi
          </Button>
        </Box>
      </Container>
    </Box>
  );
}
