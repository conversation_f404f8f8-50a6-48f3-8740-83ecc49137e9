'use client';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';

import { LomaContactFAQ } from './loma-contact-faq';
import { LomaContactHero } from './loma-contact-hero';
import { LomaContactProblems } from './loma-contact-problems';
import { LomaContactNavigation } from './loma-contact-navigation';
import { useGHLPopup, LomaContactGHLPopup } from './loma-contact-ghl-popup';

// ----------------------------------------------------------------------

export function LomaContactView() {
  const { showGHLPopup } = useGHLPopup();

  return (
    <Box sx={{ overflow: 'hidden' }}>
      {/* Hero Section - Giới thiệu AI chatbot */}
      <LomaContactHero onOpenForm={showGHLPopup} />

      <Stack
        sx={{
          position: 'relative',
          bgcolor: 'background.default',
          width: '100%',
          maxWidth: '100vw',
        }}
      >
        {/* AI Chatbot Integration - Ưu tiên hàng đầu */}
        {/* <LomaContactChatbot /> */}

        {/* Contact Methods - Focus AI và Zalo */}
        {/* <LomaContactMethods /> */}

        {/* Quick Navigation */}
        <LomaContactNavigation onOpenForm={showGHLPopup} />

        {/* Problem-Solution Matrix */}
        <LomaContactProblems onOpenForm={showGHLPopup} />

        {/* Smart FAQ */}
        <LomaContactFAQ onOpenForm={showGHLPopup} />

        {/* Contact Form - Vị trí cuối */}
        {/* <LomaContactForm onOpenForm={showGHLPopup} /> */}

        {/* Location & Info */}
        {/* <LomaContactLocation /> */}
      </Stack>

      {/* Load GHL Popup Script */}
      <LomaContactGHLPopup />
    </Box>
  );
}
