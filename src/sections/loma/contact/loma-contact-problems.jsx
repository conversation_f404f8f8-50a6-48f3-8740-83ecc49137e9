'use client';

import { useState } from 'react';
import { m } from 'framer-motion';
import { varAlpha } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Collapse from '@mui/material/Collapse';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { LOMA_CONTACT_INFO } from 'src/config/contact-info';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

const PROBLEM_SOLUTIONS = [
  {
    id: 1,
    problem: "Không biết giá túi vải in logo",
    icon: 'solar:question-circle-bold',
    color: 'primary',
    solutions: [
      {
        title: 'Báo giá tự động',
        description: 'Công cụ tính giá thông minh, kết quả trong 3 phút',
        action: 'Tính giá ngay',
        href: paths.quote,
        icon: 'solar:calculator-bold',
      },
      {
        title: 'Chat với AI',
        description: 'Hỏi AI về giá cả với thông số cụ thể',
        action: 'Hỏi AI',
        href: 'popup',
        icon: 'solar:chat-round-call-bold',
      },
    ],
  },
  {
    id: 2,
    problem: "Muốn xem logo trên túi trước khi đặt",
    icon: 'solar:eye-bold',
    color: 'success',
    solutions: [
      {
        title: 'Mockup Studio 3D',
        description: 'Upload logo và xem trước thiết kế thực tế',
        action: 'Tạo mockup',
        href: paths.mockup,
        icon: 'solar:palette-bold-duotone',
      },
      {
        title: 'Thư viện mẫu',
        description: 'Xem hàng trăm mẫu thiết kế có sẵn',
        action: 'Xem mẫu',
        href: paths.products,
        icon: 'solar:gallery-bold',
      },
    ],
  },
  {
    id: 3,
    problem: "Không biết chọn chất liệu nào",
    icon: 'solar:atom-bold',
    color: 'warning',
    solutions: [
      {
        title: 'So sánh chất liệu',
        description: 'Bảng so sánh chi tiết các loại vải và ứng dụng',
        action: 'So sánh ngay',
        href: paths.materials,
        icon: 'solar:scale-bold',
      },
      {
        title: 'Tư vấn AI',
        description: 'AI sẽ gợi ý chất liệu phù hợp với mục đích sử dụng',
        action: 'Tư vấn AI',
        href: 'popup',
        icon: 'solar:chat-round-call-bold',
      },
    ],
  },
  {
    id: 4,
    problem: "Lo lắng về chất lượng sản phẩm",
    icon: 'solar:shield-check-bold',
    color: 'info',
    solutions: [
      {
        title: 'Quy trình 6 bước',
        description: 'Xem chi tiết quy trình kiểm soát chất lượng',
        action: 'Xem quy trình',
        href: paths.process,
        icon: 'solar:settings-bold',
      },
      {
        title: 'Cam kết bảo hành 30 ngày',
        description: 'Cam kết bảo hành 30 ngày cho túi vải in logo',
        action: 'Xem chính sách',
        href: paths.process,
        icon: 'solar:shield-check-bold',
      },
    ],
  },
  {
    id: 5,
    problem: "Cần giao hàng gấp",
    icon: 'solar:clock-circle-bold',
    color: 'error',
    solutions: [
      {
        title: 'Dịch vụ cấp tốc',
        description: 'Sản xuất nhanh 5-7 ngày với dịch vụ ưu tiên',
        action: 'Đặt hàng',
        href: paths.quote,
        icon: 'solar:rocket-bold-duotone',
        comingSoon: true,
      },
      {
        title: 'Liên hệ Zalo',
        description: 'Nhắn tin trực tiếp với chuyên viên tư vấn',
        action: 'Nhắn tin',
        href: LOMA_CONTACT_INFO.urls.zalo,
        icon: 'solar:chat-round-call-bold',
      },
    ],
  },
  {
    id: 6,
    problem: "Không có kinh nghiệm thiết kế",
    icon: 'solar:palette-bold',
    color: 'secondary',
    solutions: [
      {
        title: 'Dịch vụ thiết kế',
        description: 'Đội ngũ designer hỗ trợ thiết kế logo miễn phí',
        action: 'Yêu cầu thiết kế',
        href: paths.contact,
        icon: 'solar:pen-new-square-bold',
      },
      {
        title: 'Thư viện template',
        description: 'Hàng trăm mẫu thiết kế có sẵn để tham khảo',
        action: 'Xem template',
        href: paths.products,
        icon: 'solar:document-text-bold',
      },
    ],
  },
];

// ----------------------------------------------------------------------

export function LomaContactProblems({ onOpenForm }) {
  const theme = useTheme();
  const [expandedProblem, setExpandedProblem] = useState(null);

  const handleToggleProblem = (problemId) => {
    setExpandedProblem(expandedProblem === problemId ? null : problemId);
  };

  const renderProblemCard = (problem) => {
    const isExpanded = expandedProblem === problem.id;

    return (
      <m.div key={problem.id} variants={varFade('inUp')}>
        <Card
          sx={{
            overflow: 'hidden',
            transition: 'all 0.3s ease',
            border: `1px solid ${alpha(theme.palette[problem.color].main, 0.12)}`,
            '&:hover': {
              boxShadow: `0 8px 32px ${alpha(theme.palette[problem.color].main, 0.15)}`,
            },
          }}
        >
          {/* Problem Header */}
          <Box
            onClick={() => handleToggleProblem(problem.id)}
            sx={{
              p: 3,
              cursor: 'pointer',
              background: `linear-gradient(135deg, ${alpha(theme.palette[problem.color].main, 0.05)}, ${alpha(theme.palette[problem.color].main, 0.02)})`,
              transition: 'all 0.3s ease',
              '&:hover': {
                background: `linear-gradient(135deg, ${alpha(theme.palette[problem.color].main, 0.08)}, ${alpha(theme.palette[problem.color].main, 0.04)})`,
              },
            }}
          >
            <Stack direction="row" spacing={2} alignItems="center">
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: 1.5,
                  background: `linear-gradient(135deg, ${alpha(theme.palette[problem.color].main, 0.1)}, ${alpha(theme.palette[problem.color].main, 0.2)})`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Iconify
                  icon={problem.icon}
                  sx={{
                    width: 24,
                    height: 24,
                    color: `${problem.color}.main`,
                  }}
                />
              </Box>

              <Box sx={{ flex: 1 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                  {problem.problem}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {isExpanded ? 'Thu gọn giải pháp' : 'Xem giải pháp'}
                </Typography>
              </Box>

              <Iconify
                icon={isExpanded ? 'solar:alt-arrow-up-bold' : 'solar:alt-arrow-down-bold'}
                sx={{
                  width: 20,
                  height: 20,
                  color: `${problem.color}.main`,
                  transition: 'transform 0.3s ease',
                }}
              />
            </Stack>
          </Box>

          {/* Solutions */}
          <Collapse in={isExpanded}>
            <Box sx={{ p: 3, pt: 1 }}>
              <Grid container spacing={2}>
                {problem.solutions.map((solution, solutionIndex) => (
                  <Grid key={solutionIndex} size={{ xs: 12, sm: 6 }}>
                    <Card
                      sx={{
                        p: 2.5,
                        height: '100%',
                        border: `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: `0 8px 24px ${alpha(theme.palette[problem.color].main, 0.12)}`,
                          border: `1px solid ${alpha(theme.palette[problem.color].main, 0.2)}`,
                        },
                      }}
                    >
                      <Stack spacing={2} sx={{ height: '100%' }}>
                        <Stack direction="row" spacing={1.5} alignItems="center">
                          <Box
                            sx={{
                              width: 32,
                              height: 32,
                              borderRadius: 1,
                              background: `linear-gradient(135deg, ${alpha(theme.palette[problem.color].main, 0.1)}, ${alpha(theme.palette[problem.color].main, 0.15)})`,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <Iconify
                              icon={solution.icon}
                              sx={{
                                width: 16,
                                height: 16,
                                color: `${problem.color}.main`,
                              }}
                            />
                          </Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {solution.title}
                          </Typography>
                        </Stack>

                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            {solution.description}
                          </Typography>

                          {solution.comingSoon && (
                            <Box
                              sx={{
                                mt: 1,
                                px: 1.5,
                                py: 0.5,
                                borderRadius: 1,
                                background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)}, ${alpha(theme.palette.warning.main, 0.15)})`,
                                border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
                                display: 'inline-flex',
                                alignItems: 'center',
                                gap: 0.5,
                              }}
                            >
                              <Iconify
                                icon="solar:star-bold"
                                sx={{
                                  width: 12,
                                  height: 12,
                                  color: 'warning.main',
                                }}
                              />
                              <Typography
                                variant="caption"
                                sx={{
                                  fontWeight: 600,
                                  color: 'warning.main',
                                  fontSize: '0.75rem',
                                }}
                              >
                                Sắp ra mắt
                              </Typography>
                            </Box>
                          )}
                        </Box>

                        <Button
                          component={solution.href.startsWith('#') || solution.href === 'popup' ? 'button' : RouterLink}
                          href={solution.href.startsWith('#') || solution.href === 'popup' ? undefined : solution.href}
                          onClick={
                            solution.href === 'popup' ? onOpenForm :
                              solution.href.startsWith('#') ? () => {
                                document.querySelector(solution.href)?.scrollIntoView({ behavior: 'smooth' });
                              } : undefined
                          }
                          variant={solution.comingSoon ? "outlined" : "outlined"}
                          color={solution.comingSoon ? "warning" : problem.color}
                          size="small"
                          fullWidth
                          disabled={solution.comingSoon}
                          endIcon={
                            solution.comingSoon ?
                              <Iconify icon="solar:clock-circle-bold" /> :
                              <Iconify icon="solar:arrow-right-bold" />
                          }
                          sx={{
                            py: 1,
                            fontWeight: 600,
                            borderWidth: 1.5,
                            position: 'relative',
                            overflow: 'hidden',
                            '&:hover': {
                              borderWidth: 1.5,
                            },
                            ...(solution.comingSoon && {
                              background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.02)}, ${alpha(theme.palette.warning.main, 0.05)})`,
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: '-100%',
                                width: '100%',
                                height: '100%',
                                background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.warning.main, 0.1)}, transparent)`,
                                animation: 'shimmer 2s infinite',
                              },
                              '@keyframes shimmer': {
                                '0%': { left: '-100%' },
                                '100%': { left: '100%' },
                              },
                            }),
                          }}
                        >
                          {solution.comingSoon ? 'Sắp ra mắt' : solution.action}
                        </Button>
                      </Stack>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Collapse>
        </Card>
      </m.div>
    );
  };

  return (
    <Box
      sx={{
        py: { xs: 8, md: 12 },
        background: `linear-gradient(135deg, ${varAlpha(theme.vars.palette.primary.mainChannel, 0.02)} 0%, transparent 50%, ${varAlpha(theme.vars.palette.secondary.mainChannel, 0.02)} 100%)`,
      }}
    >
      <Container component={MotionViewport}>
        <Stack spacing={5}>
          {/* Section Header */}
          <m.div variants={varFade('inUp')}>
            <Stack spacing={3} sx={{ textAlign: 'center', maxWidth: 600, mx: 'auto' }}>
              <Typography variant="h2" sx={{ fontWeight: 700 }}>
                Vấn đề của bạn?
                <Box component="span" sx={{ color: 'primary.main' }}> Chúng tôi có giải pháp!</Box>
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Mỗi vấn đề đều có giải pháp cụ thể. Click vào vấn đề bạn đang gặp phải
                để xem các công cụ và dịch vụ phù hợp.
              </Typography>
            </Stack>
          </m.div>

          {/* Problems Grid */}
          <Grid container spacing={3}>
            {PROBLEM_SOLUTIONS.map((problem) => (
              <Grid key={problem.id} size={{ xs: 12, md: 6 }}>
                {renderProblemCard(problem)}
              </Grid>
            ))}
          </Grid>

          {/* Bottom Note */}
          <m.div variants={varFade('inUp')}>
            <Box
              sx={{
                textAlign: 'center',
                p: 4,
                borderRadius: 3,
                bgcolor: varAlpha(theme.vars.palette.grey['500Channel'], 0.04),
                border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.08)}`,
              }}
            >
              <Stack spacing={2} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  <Box component="span" sx={{ fontWeight: 600 }}>
                    Không tìm thấy vấn đề của bạn?
                  </Box>
                  {' '}
                  Hãy chat trực tiếp với AI hoặc liên hệ đội ngũ hỗ trợ để được tư vấn cá nhân hóa.
                </Typography>
                <Button
                  variant="outlined"
                  size="medium"
                  startIcon={<Iconify icon="solar:document-text-bold" />}
                  onClick={onOpenForm}
                  sx={{
                    py: 1,
                    px: 3,
                    fontWeight: 600,
                    borderRadius: 2,
                    borderWidth: 1.5,
                    '&:hover': {
                      borderWidth: 1.5,
                      transform: 'translateY(-2px)',
                    },
                  }}
                >
                  Điền form liên hệ chi tiết
                </Button>
              </Stack>
            </Box>
          </m.div>
        </Stack>
      </Container>
    </Box>
  );
}
