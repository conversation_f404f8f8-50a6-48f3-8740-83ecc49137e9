'use client';

import { useState } from 'react';
import { m } from 'framer-motion';
import { varAlpha } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Collapse from '@mui/material/Collapse';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

const FAQ_DATA = [
  {
    id: 1,
    question: 'Tôi có thể biết giá túi vải cụ thể ngay không?',
    answer: 'Hoàn toàn có thể! Tất cả giá túi vải đều được công khai minh bạch trên website. Không có giá ẩn. Bạn có thể sử dụng công cụ báo giá tự động để tính chính xác chi phí theo số lượng và mẫu bạn chọn. Giá từ 18.000 - 65.000đ/chiếc tùy chất liệu và số lượng.',
    category: 'pricing',
    actions: [
      { label: 'Xem giá ngay', href: paths.quote, icon: 'solar:calculator-bold' },
      { label: 'Chat tư vấn AI', href: 'popup', icon: 'solar:chat-round-call-bold' },
    ],
  },
  {
    id: 2,
    question: 'Làm sao để biết túi có đẹp như hình không?',
    answer: 'Đây là lo lắng chính đáng! Bạn có thể: (1) Tạo mockup miễn phí ngay trên website để xem trước thiết kế, (2) Mua túi mẫu để cầm nắm thực tế chỉ với giá gốc, (3) Đặt làm mẫu thử in logo thật chỉ cọc 500k (hoàn lại khi đặt hàng). Nhìn hình thôi thì chưa đủ, phải trải nghiệm thực tế!',
    category: 'quality',
    actions: [
      { label: 'Tạo mockup ngay', href: paths.mockup, icon: 'solar:eye-bold' },
      { label: 'Mua túi mẫu', href: '/landing', icon: 'solar:bag-4-bold' },
      { label: 'Đặt mẫu thử', href: 'popup', icon: 'solar:document-add-bold' },
    ],
  },
  {
    id: 3,
    question: 'Đặt ít túi có được không? Tôi mới bắt đầu bán online',
    answer: 'Nếu bạn mới bán hàng online, chúng tôi khuyên bạn nên tìm hiểu về mô hình Affiliate Marketing. Đây là mô hình không cần phải tồn kho - đặt ra không bán được hàng, hãy bắt đầu nhỏ và tìm kiếm khách hàng rồi sau đó hẵn đặt hàng thương hiệu và bán hàng. Chúng tôi chỉ sản xuất với số lượng tối thiểu là 50 túi.',
    category: 'pricing',
    actions: [
      { label: 'Xem bảng giá chi tiết', href: paths.quote, icon: 'solar:document-text-bold' },
    ],
  },
  {
    id: 4,
    question: 'Túi có thực sự chất lượng cao cấp không?',
    answer: 'Chúng tôi cam kết chất lượng hàng quà tặng cao cấp, không phải hàng giá rẻ thường thấy. Túi được kiểm tra 6 bước nghiêm ngặt, cắt chỉ thừa tỉ mỉ. Chất liệu Canvas dày dặn, Cotton mềm mại, đạt tiêu chuẩn quà tặng doanh nghiệp. Bảo hành 30 ngày mọi lỗi từ sản xuất.',
    category: 'quality',
    actions: [
      { label: 'Xem quy trình 6 bước', href: paths.process, icon: 'solar:settings-bold' },
      { label: 'Mua mẫu để kiểm tra', href: '/landing', icon: 'solar:magnifer-bold' },
    ],
  },
  {
    id: 5,
    question: 'Tôi có logo sẵn nhưng không biết có in đẹp không?',
    answer: 'Đừng lo! Trước tiên bạn tạo mockup miễn phí để xem trước. Nếu thích, đặt làm mẫu thử thực tế với logo của bạn (cọc 500k, hoàn lại khi sản xuất). Nếu chưa hài lòng, chỉnh sửa tối đa 3 lần miễn phí. Chỉ sản xuất số lượng lớn khi bạn hoàn toàn hài lòng.',
    category: 'design',
    actions: [
      { label: 'Tạo mockup logo', href: paths.mockup, icon: 'solar:eye-bold' },
      { label: 'Đặt mẫu thử 500k', href: 'popup', icon: 'solar:document-add-bold' },
    ],
  },
  {
    id: 6,
    question: 'Sao biết được tiến độ sản xuất? Sợ bị trễ hàng',
    answer: 'Hoàn toàn minh bạch! Chúng tôi cập nhật liên tục tiến độ sản xuất qua tin nhắn/email. Bạn sẽ biết túi đang ở bước nào trong quy trình 6 bước. Không như các xưởng khác báo trễ phút chót, chúng tôi cam kết thời gian và luôn thông báo trước nếu có vấn đề.',
    category: 'production',
    actions: [
      { label: 'Xem quy trình theo dõi', href: paths.process, icon: 'solar:clock-circle-bold' },
      { label: 'Chat tư vấn', href: 'popup', icon: 'solar:chat-round-call-bold' },
    ],
  },
  {
    id: 7,
    question: 'Thanh toán như thế nào? Có an toàn không?',
    answer: 'Hoàn toàn an toàn và minh bạch! Cọc 50% khi ký hợp đồng, thanh toán 50% khi nhận hàng. Không công nợ. Tất cả giao dịch qua tài khoản công ty có hóa đơn VAT. Bạn sẽ có hợp đồng rõ ràng và được bảo hành 30 ngày.',
    category: 'payment',
    actions: [
      { label: 'Xem mẫu hợp đồng', href: 'popup', icon: 'solar:document-text-bold' },
      { label: 'Chat tư vấn thanh toán', href: 'popup', icon: 'solar:card-bold' },
    ],
  },
  {
    id: 8,
    question: 'Mẫu túi có nhiều không? Có độc đáo không?',
    answer: 'Chúng tôi có hàng chục mẫu túi đẹp, hiện đại phù hợp làm quà tặng khách hàng. Từ túi canvas cơ bản đến túi cotton cao cấp. Bạn có thể xem tất cả và thử nghiệm mockup với logo của mình. Hoặc mua túi mẫu để trải nghiệm thực tế.',
    category: 'product',
    actions: [
      { label: 'Xem tất cả mẫu túi', href: paths.products, icon: 'solar:bag-4-bold' },
      { label: 'Mua túi mẫu', href: '/landing', icon: 'solar:shopping-cart-bold' },
    ],
  },
];

const FAQ_CATEGORIES = [
  { id: 'all', label: 'Tất cả', icon: 'solar:list-bold', count: FAQ_DATA.length },
  { id: 'pricing', label: 'Giá cả', icon: 'solar:dollar-bold', count: FAQ_DATA.filter(faq => faq.category === 'pricing').length },
  { id: 'quality', label: 'Chất lượng', icon: 'solar:medal-star-bold', count: FAQ_DATA.filter(faq => faq.category === 'quality').length },
  { id: 'design', label: 'Thiết kế', icon: 'solar:palette-2-bold', count: FAQ_DATA.filter(faq => faq.category === 'design').length },
  { id: 'production', label: 'Sản xuất', icon: 'solar:settings-bold', count: FAQ_DATA.filter(faq => faq.category === 'production').length },
  { id: 'payment', label: 'Thanh toán', icon: 'solar:card-bold', count: FAQ_DATA.filter(faq => faq.category === 'payment').length },
  { id: 'product', label: 'Sản phẩm', icon: 'solar:bag-4-bold', count: FAQ_DATA.filter(faq => faq.category === 'product').length },
];

// ----------------------------------------------------------------------

export function LomaContactFAQ({ onOpenForm }) {
  const theme = useTheme();
  const [activeCategory, setActiveCategory] = useState('all');
  const [expandedFAQ, setExpandedFAQ] = useState(null);

  const filteredFAQs = activeCategory === 'all'
    ? FAQ_DATA
    : FAQ_DATA.filter(faq => faq.category === activeCategory);

  const handleToggleFAQ = (faqId) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const renderCategoryFilter = () => (
    <Stack
      direction={{ xs: 'column', sm: 'row' }}
      spacing={1}
      sx={{
        p: { xs: 0.5, sm: 1 },
        borderRadius: 2,
        bgcolor: varAlpha(theme.vars.palette.grey['500Channel'], 0.08),
        border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)}`,
        overflowX: { xs: 'auto', sm: 'visible' },
        '& > *': {
          flexShrink: 0,
        },
      }}
    >
      {FAQ_CATEGORIES.map((category) => (
        <Button
          key={category.id}
          variant={activeCategory === category.id ? 'contained' : 'text'}
          color={activeCategory === category.id ? 'primary' : 'inherit'}
          startIcon={<Iconify icon={category.icon} />}
          onClick={() => setActiveCategory(category.id)}
          sx={{
            py: { xs: 0.75, sm: 1 },
            px: { xs: 1.5, sm: 2 },
            borderRadius: 1.5,
            fontWeight: 600,
            textTransform: 'none',
            fontSize: { xs: '0.875rem', sm: '1rem' },
            minWidth: { xs: 'auto', sm: 'auto' },
            '&:hover': {
              bgcolor: activeCategory === category.id ? undefined : varAlpha(theme.vars.palette.primary.mainChannel, 0.08),
            },
          }}
        >
          {category.label} ({category.count})
        </Button>
      ))}
    </Stack>
  );

  const renderFAQItem = (faq, index) => {
    const isExpanded = expandedFAQ === faq.id;

    return (
      <m.div key={faq.id} variants={varFade('inUp')}>
        <Card
          sx={{
            overflow: 'hidden',
            transition: 'all 0.3s ease',
            border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)}`,
            '&:hover': {
              boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.08)}`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
            },
          }}
        >
          {/* Question Header */}
          <Box
            onClick={() => handleToggleFAQ(faq.id)}
            sx={{
              p: 3,
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                bgcolor: varAlpha(theme.vars.palette.primary.mainChannel, 0.04),
              },
            }}
          >
            <Stack direction="row" spacing={2} alignItems="center">
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 1,
                  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 700 }}>
                  {String(index + 1).padStart(2, '0')}
                </Typography>
              </Box>

              <Box sx={{ flex: 1 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                  {faq.question}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {isExpanded ? 'Thu gọn câu trả lời' : 'Xem câu trả lời chi tiết'}
                </Typography>
              </Box>

              <Iconify
                icon={isExpanded ? 'solar:alt-arrow-up-bold' : 'solar:alt-arrow-down-bold'}
                sx={{
                  width: 24,
                  height: 24,
                  color: 'primary.main',
                  transition: 'transform 0.3s ease',
                }}
              />
            </Stack>
          </Box>

          {/* Answer */}
          <Collapse in={isExpanded}>
            <Box sx={{ px: 3, pb: 3 }}>
              <Box
                sx={{
                  p: 3,
                  borderRadius: 2,
                  bgcolor: varAlpha(theme.vars.palette.grey['500Channel'], 0.04),
                  border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.08)}`,
                }}
              >
                <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7 }}>
                  {faq.answer}
                </Typography>

                {/* Action Buttons */}
                {faq.actions && (
                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                    {faq.actions.map((action, actionIndex) => (
                      <Button
                        key={actionIndex}
                        component={action.href.startsWith('#') || action.href === 'popup' ? 'button' : RouterLink}
                        href={action.href.startsWith('#') || action.href === 'popup' ? undefined : action.href}
                        onClick={
                          action.href === 'popup' ? onOpenForm :
                            action.href.startsWith('#') ? () => {
                              document.querySelector(action.href)?.scrollIntoView({ behavior: 'smooth' });
                            } : undefined
                        }
                        variant="outlined"
                        startIcon={<Iconify icon={action.icon} />}
                        sx={{
                          py: 1,
                          px: 2,
                          fontWeight: 600,
                          borderRadius: 1.5,
                          textTransform: 'none',
                        }}
                      >
                        {action.label}
                      </Button>
                    ))}
                  </Stack>
                )}
              </Box>
            </Box>
          </Collapse>
        </Card>
      </m.div>
    );
  };

  return (
    <Box
      sx={{
        py: { xs: 8, md: 12 },
        background: `linear-gradient(135deg, ${varAlpha(theme.vars.palette.primary.mainChannel, 0.02)} 0%, transparent 50%, ${varAlpha(theme.vars.palette.secondary.mainChannel, 0.02)} 100%)`,
      }}
    >
      <Container component={MotionViewport} id="faq">
        <Stack spacing={5}>
          {/* Section Header */}
          <m.div variants={varFade('inUp')}>
            <Stack spacing={3} sx={{ textAlign: 'center', maxWidth: 700, mx: 'auto' }}>
              <Typography variant="h2" sx={{ fontWeight: 700 }}>
                Những câu hỏi
                <Box component="span" sx={{ color: 'primary.main' }}> bạn đang thắc mắc</Box>
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem', lineHeight: 1.6 }}>
                Dành riêng cho các bạn bán online! Mọi thông tin đều minh bạch, không giá ẩn.
                Từ xem giá, thử mockup đến mua mẫu - tất cả đều tự động và nhanh chóng.
              </Typography>
            </Stack>
          </m.div>

          {/* Category Filter */}
          <m.div variants={varFade('inUp')}>
            {renderCategoryFilter()}
          </m.div>

          {/* FAQ List */}
          <Stack spacing={2}>
            {filteredFAQs.map((faq, index) => renderFAQItem(faq, index))}
          </Stack>

          {/* Bottom CTA */}
          <m.div variants={varFade('inUp')}>
            <Box
              sx={{
                textAlign: 'center',
                p: 4,
                borderRadius: 3,
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.08)}, ${alpha(theme.palette.success.main, 0.08)})`,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.12)}`,
              }}
            >
              <Stack spacing={3} alignItems="center">
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.success.main})`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Iconify icon="solar:question-circle-bold" sx={{ width: 32, height: 32, color: 'white' }} />
                </Box>

                <Stack spacing={1} sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    Bắt đầu ngay hôm nay!
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Hãy trải nghiệm quy trình minh bạch và tự động của chúng tôi
                  </Typography>
                </Stack>

                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<Iconify icon="solar:calculator-bold" />}
                    component={RouterLink}
                    href={paths.baogia}
                    sx={{
                      background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #FF5252, #26C6DA)',
                      },
                    }}
                  >
                    Xem Giá Chính Xác
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    startIcon={<Iconify icon="solar:document-text-bold" />}
                    onClick={onOpenForm}
                  >
                    Điền Form Chi Tiết
                  </Button>
                </Stack>
              </Stack>
            </Box>
          </m.div>
        </Stack>
      </Container>
    </Box>
  );
}
