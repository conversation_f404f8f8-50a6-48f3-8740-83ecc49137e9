'use client';

import { m } from 'framer-motion';
import { varAlpha } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { CONFIG, getLomaZaloUrl } from 'src/global-config';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

const NAVIGATION_ITEMS = [
  {
    title: '<PERSON><PERSON><PERSON> giá tự động',
    description: 'Tính giá chính xác ngay tức thì với công cụ báo giá thông minh',
    icon: 'solar:calculator-bold',
    color: 'primary',
    href: paths.quote,
    features: ['Báo giá tức thời', 'Nhiều tùy chọn', 'So sánh giá'],
    badge: 'Phổ biến nhất',
  },
  {
    title: 'Xem mẫu túi',
    description: 'Khám phá bộ sưu tập mẫu túi mới nhất của Loma',
    icon: 'solar:bag-4-bold',
    color: 'secondary',
    href: paths.products,
    features: ['50+ mẫu túi', 'Thông số chi tiết', 'Hình ảnh thực tế'],
    badge: null,
  },
  {
    title: 'Tạo mockup',
    description: 'Upload logo của bạn và xem trước thiết kế trên túi vải ngay tức thì',
    icon: 'solar:palette-bold-duotone',
    color: 'success',
    href: paths.mockup,
    features: ['Công cụ tiện lợi', 'Upload logo', 'Xem trước thực tế'],
    badge: 'Miễn phí',
  },
  {
    title: 'Quy trình sản xuất',
    description: 'Tìm hiểu 6 bước sản xuất đảm bảo chất lượng và thời gian',
    icon: 'solar:settings-bold',
    color: 'warning',
    href: paths.process,
    features: ['6 bước chi tiết', 'Báo cáo tiến độ liên tục', 'Kiểm soát chất lượng'],
    badge: null,
  },
  {
    title: 'Chất liệu & công nghệ',
    description: 'Khám phá các loại vải và công nghệ in hiện đại của Loma',
    icon: 'solar:atom-bold',
    color: 'info',
    href: paths.materials,
    features: ['Nhiều chất liệu', 'Công nghệ in', 'Chứng nhận chất lượng'],
    badge: null,
  },
  {
    title: 'Trò chuyện với AI',
    description: 'Tìm hiểu chi tiết về LOMA Bags, sản phẩm và dịch vụ của chúng tôi',
    icon: 'solar:chat-round-call-bold',
    color: 'error',
    href: '/chat',
    features: ['Tư vấn nhanh chóng', 'Hỗ trợ 24/7', 'Thông minh và chính xác'],
    badge: null,
  },
];

// ----------------------------------------------------------------------

export function LomaContactNavigation({ onOpenForm }) {
  const theme = useTheme();

  const renderNavigationCard = (item, index) => (
    <m.div key={index} variants={varFade('inUp')} style={{ height: '100%', display: 'flex' }}>
      <Card
        sx={{
          p: 3,
          height: '100%',
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.3s ease',
          border: `1px solid ${alpha(theme.palette[item.color].main, 0.12)}`,
          '&:hover': {
            transform: 'translateY(-8px)',
            boxShadow: `0 20px 40px ${alpha(theme.palette[item.color].main, 0.15)}`,
            border: `1px solid ${alpha(theme.palette[item.color].main, 0.3)}`,
          },
        }}
      >
        {/* Badge */}
        {item.badge && (
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              px: 1.5,
              py: 0.5,
              borderRadius: 1,
              bgcolor: `${item.color}.main`,
              color: `${item.color}.contrastText`,
              typography: 'caption',
              fontWeight: 600,
              fontSize: 10,
            }}
          >
            {item.badge}
          </Box>
        )}

        <Stack spacing={3} sx={{ height: '100%' }}>
          {/* Icon */}
          <Box
            sx={{
              width: 64,
              height: 64,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette[item.color].main, 0.1)}, ${alpha(theme.palette[item.color].main, 0.2)})`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Iconify
              icon={item.icon}
              sx={{
                width: 32,
                height: 32,
                color: `${item.color}.main`,
              }}
            />
          </Box>

          {/* Content */}
          <Stack spacing={2} sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {item.title}
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
              {item.description}
            </Typography>

            {/* Features */}
            <Stack spacing={1}>
              {item.features.map((feature, featureIndex) => (
                <Stack key={featureIndex} direction="row" spacing={1} alignItems="center">
                  <Iconify
                    icon="solar:check-circle-bold"
                    sx={{
                      width: 16,
                      height: 16,
                      color: `${item.color}.main`,
                    }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {feature}
                  </Typography>
                </Stack>
              ))}
            </Stack>
          </Stack>

          {/* Action Button */}
          <Button
            component={RouterLink}
            href={item.href}
            variant="outlined"
            color={item.color}
            fullWidth
            endIcon={<Iconify icon="solar:arrow-right-bold" />}
            sx={{
              py: 1.5,
              fontWeight: 600,
              borderWidth: 2,
              '&:hover': {
                borderWidth: 2,
                transform: 'translateY(-2px)',
              },
            }}
          >
            Khám phá ngay
          </Button>
        </Stack>

        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: -50,
            right: -50,
            width: 100,
            height: 100,
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(theme.palette[item.color].main, 0.05)} 0%, transparent 70%)`,
            zIndex: 0,
          }}
        />
      </Card>
    </m.div>
  );

  return (
    <Box
      sx={{
        py: { xs: 8, md: 12 },
        background: `linear-gradient(135deg, ${varAlpha(theme.vars.palette.grey['500Channel'], 0.02)} 0%, transparent 100%)`,
      }}
    >
      <Container component={MotionViewport}>
        <Stack spacing={5}>
          {/* Section Header */}
          <m.div variants={varFade('inUp')}>
            <Stack spacing={3} sx={{ textAlign: 'center', maxWidth: 600, mx: 'auto' }}>
              <Typography variant="h2" sx={{ fontWeight: 700 }}>
                Tiện ích
                <Box component="span" sx={{ color: 'primary.main' }}> thông minh</Box>
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Chọn đúng công cụ cho nhu cầu của bạn. Mỗi phần được thiết kế để giải quyết
                một vấn đề cụ thể một cách nhanh chóng và hiệu quả.
              </Typography>
            </Stack>
          </m.div>

          {/* Navigation Grid */}
          <Grid container spacing={3}>
            {NAVIGATION_ITEMS.map((item, index) => (
              <Grid key={index} size={{ xs: 12, sm: 6, md: 4 }}>
                {renderNavigationCard(item, index)}
              </Grid>
            ))}
          </Grid>

          {/* Bottom CTA */}
          <m.div variants={varFade('inUp')}>
            <Box
              sx={{
                textAlign: 'center',
                p: 4,
                borderRadius: 3,
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.08)}, ${alpha(theme.palette.secondary.main, 0.08)})`,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.12)}`,
              }}
            >
              <Stack spacing={2} alignItems="center">
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  Vẫn chưa tìm thấy thông tin cần thiết?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Liên hệ trực tiếp với đội ngũ chuyên gia của chúng tôi
                </Typography>
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<Iconify icon="solar:document-text-bold" />}
                    onClick={onOpenForm}
                    sx={{
                      background: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
                      boxShadow: '0 8px 24px rgba(255, 107, 53, 0.4)',
                      '&:hover': {
                        boxShadow: '0 12px 32px rgba(255, 107, 53, 0.5)',
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    Điền form liên hệ
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    startIcon={<Iconify icon="/assets/icons/zalo_icon.svg" />}
                    href={getLomaZaloUrl()}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Nhắn Zalo: {CONFIG.loma.contact.phoneInternational}
                  </Button>
                </Stack>
              </Stack>
            </Box>
          </m.div>
        </Stack>
      </Container>
    </Box>
  );
}
