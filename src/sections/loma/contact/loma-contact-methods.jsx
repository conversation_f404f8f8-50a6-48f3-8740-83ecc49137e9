'use client';

import { m } from 'framer-motion';
import { varAlpha } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { LOMA_CONTACT_INFO } from 'src/config/contact-info';

// import { LOMA_CONTACT_INFO } from 'src/config/contact-info';
import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

const CONTACT_METHODS = [
  {
    title: 'Tr<PERSON> chuyện với AI',
    description: '<PERSON>tbot AI trả lời nhanh chóng mọi thắc mắc',
    value: 'LOMA AI Assistant',
    icon: 'solar:chat-round-call-bold',
    action: 'Chat với AI',
    href: LOMA_CONTACT_INFO.urls.aiChat,
    features: ['Phản hồi tức thì', 'Hỗ trợ 24/7', 'Thông minh và chính xác'],
    badge: LOMA_CONTACT_INFO.labels.aiSmart,
    isPrimary: true,
    isAI: true,
  },
  {
    title: 'Zalo Business',
    description: 'Chat trực tiếp với chuyên viên tư vấn',
    value: LOMA_CONTACT_INFO.phone,
    icon: '/assets/icons/zalo_icon.svg',
    action: 'Nhắn Zalo',
    href: LOMA_CONTACT_INFO.urls.zalo,
    features: ['Tư vấn chuyên sâu', 'Chia sẻ hình ảnh', 'Phản hồi nhanh'],
    badge: LOMA_CONTACT_INFO.labels.zaloPriority,
    isPrimary: true,
    isZalo: true,
  },
  {
    title: 'Điền form liên hệ',
    description: 'Gửi thông tin chi tiết để được tư vấn',
    value: 'Form chính thức',
    icon: 'solar:document-text-bold',
    action: 'Điền form',
    href: 'popup',
    features: ['Thông tin chi tiết', 'Lưu trữ lịch sử', 'Phản hồi trong 2h'],
    badge: null,
    isPrimary: false,
    isForm: true,
  },
];

// ----------------------------------------------------------------------

export function LomaContactMethods({ onOpenForm }) {
  const theme = useTheme();

  const renderContactCard = (method, index) => (
    <m.div
      key={index}
      variants={varFade('inUp')}
      style={{ height: '100%', display: 'flex' }}
    >
      <Card
        sx={{
          p: 3,
          height: '100%',
          width: '100%',
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.3s ease',
          bgcolor: 'background.paper',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          display: 'flex',
          flexDirection: 'column',
          '&:hover': {
            transform: 'translateY(-8px)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
          },
        }}
      >
        {/* Badge */}
        {method.badge && (
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              px: 1.5,
              py: 0.5,
              borderRadius: 1.5,
              bgcolor: method.isAI ? '#00bcd4' :
                method.isZalo ? '#0068ff' :
                  method.isPrimary ? 'primary.main' : 'success.main',
              color: 'white',
              typography: 'caption',
              fontWeight: 600,
              fontSize: 10,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            }}
          >
            {method.badge}
          </Box>
        )}

        <Stack spacing={3} sx={{ height: '100%' }}>
          {/* Icon & Title */}
          <Stack direction="row" spacing={2} alignItems="flex-start">
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: 2,
                bgcolor: method.isAI ? alpha(theme.palette.info.main, 0.1) :
                  method.isZalo ? alpha(theme.palette.primary.main, 0.1) :
                    method.isPrimary ? alpha(theme.palette.primary.main, 0.1) :
                      alpha(theme.palette.grey[500], 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
                ...(method.isAI && {
                  background: 'linear-gradient(135deg, #00bcd4, #0097a7)',
                }),
                ...(method.isZalo && {
                  background: 'linear-gradient(135deg, #0068ff, #0052cc)',
                }),
              }}
            >
              <Iconify
                icon={method.icon}
                sx={{
                  width: 28,
                  height: 28,
                  color: method.isAI ? 'white' :
                    method.isZalo ? 'white' :
                      method.isPrimary ? 'primary.main' : 'text.secondary',
                }}
              />
            </Box>

            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                {method.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.5 }}>
                {method.description}
              </Typography>
            </Box>
          </Stack>

          {/* Contact Value */}
          <Box
            sx={{
              p: 2,
              borderRadius: 2,
              bgcolor: alpha(theme.palette.grey[500], 0.05),
              boxShadow: 'inset 0 1px 3px rgba(0, 0, 0, 0.05)',
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                textAlign: 'center',
              }}
            >
              {method.value}
            </Typography>
          </Box>

          {/* Features */}
          <Stack spacing={1} sx={{ flex: 1 }}>
            {method.features.map((feature, featureIndex) => (
              <Stack key={featureIndex} direction="row" spacing={1} alignItems="center">
                <Iconify
                  icon="solar:check-circle-bold"
                  sx={{
                    width: 16,
                    height: 16,
                    color: 'success.main',
                    flexShrink: 0,
                  }}
                />
                <Typography variant="body2" color="text.secondary">
                  {feature}
                </Typography>
              </Stack>
            ))}
          </Stack>

          {/* Action Button */}
          <Button
            component={method.href.startsWith('#') || method.href === 'popup' ? 'button' : 'a'}
            href={method.href.startsWith('#') || method.href === 'popup' ? undefined : method.href}
            onClick={
              method.href === 'popup' ? onOpenForm :
                method.href.startsWith('#') ? () => {
                  document.querySelector(method.href)?.scrollIntoView({ behavior: 'smooth' });
                } : undefined
            }
            variant={method.isPrimary ? "contained" : "outlined"}
            color={method.isPrimary ? "primary" : "inherit"}
            fullWidth
            size="large"
            endIcon={<Iconify icon="solar:arrow-right-bold" />}
            sx={{
              py: 1.5,
              fontWeight: 600,
              borderRadius: 2,
              ...(method.isAI && {
                background: 'linear-gradient(135deg, #00bcd4, #0097a7)',
                color: 'white',
                boxShadow: '0 8px 24px rgba(0, 188, 212, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #0097a7, #006064)',
                  boxShadow: '0 12px 32px rgba(0, 188, 212, 0.4)',
                  transform: 'translateY(-2px)',
                },
              }),
              ...(method.isZalo && {
                background: 'linear-gradient(135deg, #0068ff, #0052cc)',
                color: 'white',
                boxShadow: '0 8px 24px rgba(0, 104, 255, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #0052cc, #003d99)',
                  boxShadow: '0 12px 32px rgba(0, 104, 255, 0.4)',
                  transform: 'translateY(-2px)',
                },
              }),
              ...(method.isPrimary && !method.isAI && !method.isZalo && {
                boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                '&:hover': {
                  boxShadow: '0 12px 32px rgba(0, 0, 0, 0.2)',
                  transform: 'translateY(-2px)',
                },
              }),
              ...(!method.isPrimary && {
                border: 'none',
                bgcolor: alpha(theme.palette.grey[500], 0.08),
                color: 'text.primary',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                '&:hover': {
                  bgcolor: alpha(theme.palette.grey[500], 0.12),
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)',
                  transform: 'translateY(-2px)',
                },
              }),
            }}
          >
            {method.action}
          </Button>
        </Stack>

        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            bottom: -30,
            right: -30,
            width: 80,
            height: 80,
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(theme.palette.grey[500], 0.03)} 0%, transparent 70%)`,
            zIndex: 0,
          }}
        />
      </Card>
    </m.div>
  );

  return (
    <Box
      sx={{
        py: { xs: 8, md: 12 },
        background: `linear-gradient(135deg, ${varAlpha(theme.vars.palette.grey['500Channel'], 0.02)} 0%, transparent 100%)`,
      }}
    >
      <Container component={MotionViewport}>
        <Stack spacing={5}>
          {/* Section Header */}
          <m.div variants={varFade('inUp')}>
            <Stack spacing={3} sx={{ textAlign: 'center', maxWidth: 600, mx: 'auto' }}>
              <Typography variant="h2" sx={{ fontWeight: 700 }}>
                Liên hệ với
                <Box component="span" sx={{ color: 'primary.main' }}> LOMA</Box>
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Chat với AI để được tư vấn nhanh chóng, hoặc liên hệ Zalo để trao đổi trực tiếp.
                Chọn phương thức phù hợp nhất với bạn.
              </Typography>
            </Stack>
          </m.div>

          {/* Contact Methods Grid */}
          <Grid container spacing={3} sx={{ alignItems: 'stretch' }}>
            {CONTACT_METHODS.map((method, index) => (
              <Grid
                key={index}
                size={{ xs: 12, sm: 6, md: 4 }}
                sx={{ display: 'flex' }}
              >
                {renderContactCard(method, index)}
              </Grid>
            ))}
          </Grid>

          {/* Emergency Contact */}
          <m.div variants={varFade('inUp')}>
            <Box
              sx={{
                textAlign: 'center',
                p: 4,
                borderRadius: 3,
                bgcolor: 'background.paper',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
              }}
            >
              <Stack spacing={2} alignItems="center">
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    bgcolor: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                  }}
                >
                  <Iconify icon="solar:phone-calling-bold" sx={{ width: 32, height: 32, color: 'white' }} />
                </Box>
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  Cần hỗ trợ khẩn cấp?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Đối với các đơn hàng gấp hoặc vấn đề khẩn cấp, vui lòng gọi hotline
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={<Iconify icon="solar:phone-calling-bold" />}
                  href="tel:+84938069715"
                  sx={{
                    py: 1.5,
                    px: 4,
                    fontWeight: 600,
                    borderRadius: 2,
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                    '&:hover': {
                      boxShadow: '0 12px 32px rgba(0, 0, 0, 0.2)',
                    },
                  }}
                >
                  Hotline: {LOMA_CONTACT_INFO.phone}
                </Button>
              </Stack>
            </Box>
          </m.div>
        </Stack>
      </Container>
    </Box>
  );
}
