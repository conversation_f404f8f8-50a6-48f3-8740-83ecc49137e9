'use client';

import { m } from 'framer-motion';
import { varAlpha } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

export function LomaContactHero({ onOpenForm }) {
  const theme = useTheme();

  const renderContent = () => (
    <Container
      component={MotionViewport}
      sx={{
        py: { xs: 10, md: 15 },
        position: 'relative',
        zIndex: 2,
      }}
    >
      <Stack
        spacing={5}
        alignItems="center"
        sx={{
          textAlign: 'center',
          maxWidth: 800,
          mx: 'auto',
        }}
      >
        {/* Badge - Enhanced AI Chatbot */}
        <m.div variants={varFade('inUp')}>
          <Box
            sx={{
              px: 4,
              py: 2,
              borderRadius: 4,
              bgcolor: 'primary.main',
              background: `linear-gradient(135deg, ${theme.vars.palette.primary.main} 0%, ${theme.vars.palette.primary.dark} 100%)`,
              boxShadow: '0 8px 32px rgba(255, 152, 0, 0.3)',
              display: 'inline-flex',
              alignItems: 'center',
              gap: 2,
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: '-100%',
                width: '100%',
                height: '100%',
                background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                animation: 'shimmer 2s infinite',
              },
              '@keyframes shimmer': {
                '0%': { left: '-100%' },
                '100%': { left: '100%' },
              },
              '&:hover': {
                transform: 'scale(1.05)',
                boxShadow: '0 12px 40px rgba(255, 152, 0, 0.4)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            <Iconify
              icon="solar:chat-round-call-bold"
              sx={{
                color: 'white',
                width: 28,
                height: 28,
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': { transform: 'scale(1)' },
                  '50%': { transform: 'scale(1.1)' },
                  '100%': { transform: 'scale(1)' },
                },
              }}
            />
            <Typography
              variant="h6"
              sx={{
                color: 'white',
                fontWeight: 700,
                fontSize: { xs: 16, md: 18 },
                textShadow: '0 2px 4px rgba(0,0,0,0.2)',
              }}
            >
              🤖 AI CHATBOT HỖ TRỢ 24/7
            </Typography>
          </Box>
        </m.div>

        {/* Main Headline */}
        <Stack spacing={3}>
          <m.div variants={varFade('inUp')}>
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: 40, md: 56 },
                fontWeight: 800,
                lineHeight: 1.2,
                background: `linear-gradient(135deg, ${theme.vars.palette.text.primary} 0%, ${theme.vars.palette.primary.main} 100%)`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Mọi câu hỏi của bạn
              <br />
              <Box component="span" sx={{ color: 'primary.main' }}>
                đều có lời giải đáp
              </Box>
            </Typography>
          </m.div>

          <m.div variants={varFade('inUp')}>
            <Typography
              variant="h5"
              sx={{
                color: 'text.secondary',
                fontWeight: 400,
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6,
              }}
            >
              Chatbot AI thông minh sẽ tư vấn chi tiết về sản phẩm, báo giá và quy trình.
              Không cần điền form - chỉ cần hỏi và nhận câu trả lời ngay lập tức!
            </Typography>
          </m.div>
        </Stack>

        {/* CTA Buttons */}
        <m.div variants={varFade('inUp')}>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            justifyContent="center"
          >
            <Button
              variant="contained"
              size="large"
              startIcon={
                <Iconify
                  icon="solar:calculator-bold"
                  sx={{
                    width: 24,
                    height: 24,
                    animation: 'bounce 2s infinite',
                    '@keyframes bounce': {
                      '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
                      '40%': { transform: 'translateY(-4px)' },
                      '60%': { transform: 'translateY(-2px)' },
                    },
                  }}
                />
              }
              sx={{
                py: 2,
                px: 6,
                fontSize: { xs: 18, md: 20 },
                fontWeight: 700,
                borderRadius: 4,
                background: `linear-gradient(135deg, ${theme.vars.palette.primary.main} 0%, ${theme.vars.palette.primary.dark} 100%)`,
                boxShadow: '0 12px 40px rgba(255, 152, 0, 0.3)',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, transparent 70%)',
                  animation: 'ripple 3s infinite',
                },
                '@keyframes ripple': {
                  '0%': { transform: 'scale(0)', opacity: 1 },
                  '100%': { transform: 'scale(4)', opacity: 0 },
                },
                '&:hover': {
                  boxShadow: '0 16px 48px rgba(255, 152, 0, 0.4)',
                  transform: 'translateY(-3px) scale(1.02)',
                  background: `linear-gradient(135deg, ${theme.vars.palette.primary.dark} 0%, ${theme.vars.palette.primary.main} 100%)`,
                },
                transition: 'all 0.3s ease',
                textTransform: 'none',
                letterSpacing: '0.5px',
              }}
              href={paths.baogia}
            >
              🚀 BÁO GIÁ TỰ ĐỘNG
            </Button>

            <Button
              onClick={onOpenForm}
              variant="outlined"
              size="large"
              startIcon={<Iconify icon="solar:document-text-bold" />}
              sx={{
                py: 1.5,
                px: 4,
                fontSize: 16,
                fontWeight: 600,
                borderRadius: 3,
                border: 'none',
                bgcolor: 'background.paper',
                color: 'text.primary',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                '&:hover': {
                  bgcolor: 'background.paper',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease',
              }}
            >
              ĐIỀN FORM LIÊN HỆ
            </Button>
          </Stack>
        </m.div>

        {/* Quick Stats */}
        <m.div variants={varFade('inUp')}>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={{ xs: 3, sm: 5 }}
            sx={{
              mt: 4,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {[
              { icon: 'solar:clock-circle-bold', text: 'Phản hồi < 30 giây' },
              { icon: 'solar:verified-check-bold', text: '99% độ chính xác' },
              { icon: 'solar:chat-round-call-bold', text: 'Hỗ trợ 24/7' },
            ].map((stat, index) => (
              <Stack
                key={index}
                direction="row"
                spacing={1.5}
                alignItems="center"
                sx={{
                  px: 3,
                  py: 1.5,
                  borderRadius: 2,
                  bgcolor: 'background.paper',
                  boxShadow: '0 2px 12px rgba(0, 0, 0, 0.06)',
                }}
              >
                <Iconify
                  icon={stat.icon}
                  sx={{
                    color: 'text.secondary',
                    width: 18,
                    height: 18,
                  }}
                />
                <Typography
                  variant="body2"
                  sx={{
                    color: 'text.primary',
                    fontWeight: 600,
                    fontSize: 13,
                  }}
                >
                  {stat.text}
                </Typography>
              </Stack>
            ))}
          </Stack>
        </m.div>
      </Stack>
    </Container>
  );

  const renderBackground = () => (
    <Box
      sx={{
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        position: 'absolute',
        background: `linear-gradient(135deg,
          ${varAlpha(theme.vars.palette.success.mainChannel, 0.08)} 0%,
          ${theme.vars.palette.background.default} 30%,
          ${theme.vars.palette.background.default} 70%,
          ${varAlpha(theme.vars.palette.primary.mainChannel, 0.08)} 100%)`,
      }}
    />
  );

  return (
    <Box
      sx={{
        minHeight: { xs: '80vh', md: '90vh' },
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        overflow: 'hidden',
      }}
    >
      {renderBackground()}
      {renderContent()}
    </Box>
  );
}
