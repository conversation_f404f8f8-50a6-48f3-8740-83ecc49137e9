'use client';

import { useEffect } from 'react';

// Component để load GHL popup - sử dụng embed code ch<PERSON>h thức
export function LomaContactGHLPopup() {
    useEffect(() => {
        // <PERSON><PERSON><PERSON> tra xem đã có GHL form chưa
        if (!document.querySelector('#popup-q2V0z04v50hA46X0Wn8f')) {
            // Tạo iframe theo đúng format GHL
            const iframe = document.createElement('iframe');
            iframe.src = 'https://api.funnel.loma.vn/widget/form/q2V0z04v50hA46X0Wn8f';
            iframe.style.cssText = 'display:none;width:100%;height:100%;border:none;border-radius:3px';
            iframe.id = 'popup-q2V0z04v50hA46X0Wn8f';
            iframe.setAttribute('data-layout', "{'id':'POPUP'}");
            iframe.setAttribute('data-trigger-type', 'alwaysShow');
            iframe.setAttribute('data-trigger-value', '');
            iframe.setAttribute('data-activation-type', 'alwaysActivated');
            iframe.setAttribute('data-activation-value', '');
            iframe.setAttribute('data-deactivation-type', 'neverDeactivate');
            iframe.setAttribute('data-deactivation-value', '');
            iframe.setAttribute('data-form-name', 'Lien-He-Loma_bag-Contact-page');
            iframe.setAttribute('data-height', '606');
            iframe.setAttribute('data-layout-iframe-id', 'popup-q2V0z04v50hA46X0Wn8f');
            iframe.setAttribute('data-form-id', 'q2V0z04v50hA46X0Wn8f');
            iframe.title = 'Lien-He-Loma_bag-Contact-page';
            document.body.appendChild(iframe);
        }

        // Load script GHL nếu chưa có
        if (!document.querySelector('script[src="https://api.funnel.loma.vn/js/form_embed.js"]')) {
            const script = document.createElement('script');
            script.src = 'https://api.funnel.loma.vn/js/form_embed.js';
            script.async = true;
            document.body.appendChild(script);
        }

        // Thêm CSS cho popup overlay (nếu cần)
        if (!document.querySelector('#ghl-popup-style')) {
            const style = document.createElement('style');
            style.id = 'ghl-popup-style';
            style.textContent = `
                .ghl-popup-overlay {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    background-color: rgba(0, 0, 0, 0.8) !important;
                    z-index: 999999 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }
                .ghl-popup-iframe {
                    width: 90% !important;
                    max-width: 600px !important;
                    height: 80% !important;
                    max-height: 700px !important;
                    border: none !important;
                    border-radius: 8px !important;
                    background: white !important;
                }
            `;
            document.head.appendChild(style);
        }
    }, []);

    return null;
}

// Hook để sử dụng GHL popup
export function useGHLPopup() {
    const showGHLPopup = () => {

        // Kiểm tra xem FormEmbed có tồn tại không
        if (window.FormEmbed) {
            try {
                // Thử nhiều cách gọi popup
                if (typeof window.FormEmbed.showPopup === 'function') {
                    window.FormEmbed.showPopup('popup-q2V0z04v50hA46X0Wn8f');
                } else if (typeof window.FormEmbed.show === 'function') {
                    window.FormEmbed.show('popup-q2V0z04v50hA46X0Wn8f');
                } else if (typeof window.FormEmbed.trigger === 'function') {
                    window.FormEmbed.trigger('popup-q2V0z04v50hA46X0Wn8f');
                } else {
                    console.log('Available FormEmbed methods:', Object.keys(window.FormEmbed));
                }
            } catch (error) {
                console.error('Error');
            }
        } else {

            // Fallback: thử trigger iframe trực tiếp
            const iframe = document.getElementById('popup-q2V0z04v50hA46X0Wn8f');
            if (iframe) {
                iframe.style.display = 'block';

                // Tạo overlay
                const overlay = document.createElement('div');
                overlay.id = 'ghl-popup-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.7);
                    z-index: 9999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                `;

                iframe.style.cssText = `
                    display: block;
                    width: 90%;
                    max-width: 600px;
                    height: 80%;
                    max-height: 700px;
                    border: none;
                    border-radius: 8px;
                    background: white;
                    z-index: 10000;
                `;

                overlay.appendChild(iframe);
                document.body.appendChild(overlay);

                // Thêm nút đóng
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        document.body.removeChild(overlay);
                        iframe.style.display = 'none';
                        document.body.appendChild(iframe);
                    }
                });
            } else {
                console.error('Lỗi');
            }
        }
    };

    return {
        showGHLPopup,
    };
} 