'use client';

import { z as zod } from 'zod';
import { useState } from 'react';
import { m } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { varAlpha } from 'minimal-shared/utils';
import { zodResolver } from '@hookform/resolvers/zod';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import { MenuItem } from '@mui/material';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import { alpha, useTheme } from '@mui/material/styles';

import { LOMA_CONTACT_INFO } from 'src/config/contact-info';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

const ContactFormSchema = zod.object({
  name: zod
    .string()
    .min(1, { message: 'Họ tên là bắt buộc' })
    .min(2, { message: 'Họ tên phải có ít nhất 2 ký tự' }),
  email: zod
    .string()
    .min(1, { message: 'Email là bắt buộc' })
    .email({ message: 'Email không hợp lệ' }),
  phone: zod
    .string()
    .min(1, { message: 'Số điện thoại/Zalo là bắt buộc' })
    .regex(/^(0[3|5|7|8|9])+([0-9]{8})$/, { message: 'Vui lòng nhập số điện thoại Việt Nam hợp lệ' }),
  quantity: zod
    .number({
      required_error: 'Số lượng là bắt buộc',
      invalid_type_error: 'Số lượng phải là số'
    })
    .min(50, { message: 'Số lượng tối thiểu là 50 túi' })
    .max(100000, { message: 'Số lượng tối đa là 100,000 túi' }),
  budgetPerBag: zod
    .string()
    .min(1, { message: 'Ngân sách trên 1 túi là bắt buộc' }),
  note: zod
    .string()
    .optional()
    .transform((val) => val?.trim() || ''),
});

const BUDGET_RANGES = [
  '20.000 - 30.000đ/túi',
  '30.000 - 50.000đ/túi',
  '50.000 - 100.000đ/túi',
  'Trên 100.000đ/túi',
  'Yêu cầu cao về thời trang, chất lượng',
];

// ----------------------------------------------------------------------

export function LomaContactForm({ onOpenForm }) {
  const theme = useTheme();
  const [submitState, setSubmitState] = useState({
    isSubmitting: false,
    error: null,
  });

  const methods = useForm({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      quantity: 50,
      budgetPerBag: '',
      note: '',
    },
    mode: 'onSubmit', // Chỉ validate khi submit, không validate khi đang nhập
    reValidateMode: 'onSubmit', // Chỉ re-validate khi submit lại
  });

  const {
    handleSubmit,
    reset,
    setError,
    clearErrors,
    formState: { isSubmitting, isSubmitSuccessful, errors },
  } = methods;

  // Clear errors khi user bắt đầu nhập lại
  const handleFieldChange = () => {
    if (Object.keys(errors).length > 0) {
      clearErrors();
    }
  };

  const onSubmit = async (data) => {
    try {
      // Clear previous errors khi submit
      setSubmitState({ isSubmitting: true, error: null });

      // Call the contact API
      const response = await fetch('/api/contact/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          subject: 'Đặt túi vải in logo theo yêu cầu',
          formType: 'quick_bag_order',
          message: `Khách hàng muốn đặt ${data.quantity} túi với ngân sách ${data.budgetPerBag}. ${data.note ? `\n\nGhi chú thêm: ${data.note}` : ''}\n\nVui lòng liên hệ tư vấn chi tiết.`
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Success case - redirect to thank you page
        window.location.href = '/cam-on-lien-he-dat-tui-vai';
      } else {
        // Error case - errors sẽ hiển thị trên từng field thông qua validation
        setSubmitState({
          isSubmitting: false,
          error: result.error || 'Có lỗi xảy ra. Vui lòng thử lại sau.'
        });
      }

    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitState({
        isSubmitting: false,
        error: 'Có lỗi kết nối. Vui lòng kiểm tra internet và thử lại.'
      });
    }
  };

  const renderFormCard = () => (
    <Card
      sx={{
        p: 4,
        bgcolor: 'background.paper',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
        borderRadius: 3,
      }}
    >
      <Stack spacing={3}>
        <Stack spacing={1}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            Nhận báo giá nhanh chóng
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Chỉ cần 5 thông tin cơ bản, chúng tôi sẽ gọi tư vấn chi tiết trong 30 phút
          </Typography>
        </Stack>

        {/* Error Alert */}
        {submitState.error && (
          <Alert
            severity="error"
            variant="filled"
            sx={{ borderRadius: 2 }}
          >
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5 }}>
              Có lỗi xảy ra!
            </Typography>
            <Typography variant="body2">
              {submitState.error}
            </Typography>
          </Alert>
        )}

        <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name="name"
                label="Họ và tên *"
                placeholder="Nguyễn Văn A"
                size="large"
                onFocus={handleFieldChange}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name="phone"
                label="Số điện thoại/Zalo *"
                placeholder="0901234567"
                size="large"
                onFocus={handleFieldChange}
                helperText="Ví dụ: 0901234567, +84901234567"
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <Field.Text
                name="email"
                label="Email *"
                placeholder="<EMAIL>"
                type="email"
                size="large"
                onFocus={handleFieldChange}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name="quantity"
                label="Số lượng (tối thiểu 50 túi) *"
                placeholder="50"
                type="number"
                size="large"
                onFocus={handleFieldChange}
                slotProps={{
                  htmlInput: {
                    min: 50,
                    max: 100000,
                    step: 1
                  }
                }}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Select
                name="budgetPerBag"
                label="Ngân sách trên 1 túi *"
                placeholder="Chọn mức ngân sách"
                size="large"
                onFocus={handleFieldChange}
              >
                {BUDGET_RANGES.map((budget) => (
                  <MenuItem key={budget} value={budget}>
                    {budget}
                  </MenuItem>
                ))}
              </Field.Select>
            </Grid>

            <Grid size={{ xs: 12 }}>
              <Field.Text
                name="note"
                label="Ghi chú thêm (tùy chọn)"
                placeholder="Ví dụ: Loại vải, màu sắc, kích thước đặc biệt, yêu cầu về logo, thời gian giao hàng..."
                multiline
                rows={3}
                size="large"
                onFocus={handleFieldChange}
              />
            </Grid>

          </Grid>

          <Stack direction="row" justifyContent="center" sx={{ mt: 4 }} spacing={2}>
            <LoadingButton
              type="submit"
              variant="contained"
              size="large"
              loading={submitState.isSubmitting || isSubmitting}
              endIcon={!submitState.isSubmitting && !isSubmitting && <Iconify icon="solar:phone-calling-bold" />}
              sx={{
                py: 2,
                px: 6,
                fontSize: '1.1rem',
                fontWeight: 600,
                borderRadius: 3,
                minHeight: 56,
                background: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
                boxShadow: '0 8px 24px rgba(255, 107, 53, 0.4)',
                color: '#ffffff',
                '&:hover': {
                  boxShadow: '0 12px 32px rgba(255, 107, 53, 0.5)',
                  transform: 'translateY(-2px)',
                },
              }}
            >
              Nhận tư vấn miễn phí ngay
            </LoadingButton>
          </Stack>

          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ textAlign: 'center', mt: 2, display: 'block' }}
          >
            {submitState.error ? (
              <>
                <Box component="span" sx={{ color: 'error.main', fontWeight: 600 }}>
                  {submitState.error}
                </Box>
                {' '}Hoặc nhắn Zalo{' '}
                <Box
                  component="span"
                  sx={{
                    fontWeight: 600,
                    color: 'primary.main',
                    cursor: 'pointer',
                    textDecoration: 'underline',
                    '&:hover': { opacity: 0.8 }
                  }}
                  onClick={() => window.open(LOMA_CONTACT_INFO.urls.zalo, '_blank')}
                >
                  {LOMA_CONTACT_INFO.phone}
                </Box>
                {' '}để được hỗ trợ ngay lập tức
              </>
            ) : (
              'Loma sẽ tư vấn chi tiết về chất liệu, kích thước, màu sắc và logo phù hợp'
            )}
          </Typography>
        </Form>
      </Stack>
    </Card>
  );

  const renderBenefitsCard = () => (
    <Card
      sx={{
        p: 4,
        height: 'fit-content',
        bgcolor: 'background.paper',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        borderRadius: 3,
      }}
    >
      <Stack spacing={3}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Tại sao chọn chúng tôi?
        </Typography>

        <Stack spacing={3}>
          {[
            {
              icon: 'solar:phone-calling-bold',
              title: 'Tư vấn nhanh chóng với Chatbot AI',
              description: 'AI Chatbot sẽ tư vấn chi tiết và chính xác',
            },
            {
              icon: 'solar:shield-check-bold',
              title: 'Cam kết chất lượng',
              description: 'Bảo hành 30 ngày, làm mẫu miễn phí',
            },
            {
              icon: 'solar:dollar-minimalistic-bold',
              title: 'Giá cả minh bạch',
              description: 'Báo giá rõ ràng, không phí ẩn, chất lượng cao',
            },
            {
              icon: 'solar:delivery-bold',
              title: 'Giao hàng đúng hạn',
              description: 'Giao hàng miễn phí, giao hàng toàn quốc',
            },
          ].map((item, index) => (
            <Stack key={index} direction="row" spacing={2}>
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: 1.5,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                <Iconify
                  icon={item.icon}
                  sx={{
                    width: 24,
                    height: 24,
                    color: 'primary.main',
                  }}
                />
              </Box>

              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5 }}>
                  {item.title}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {item.description}
                </Typography>
              </Box>
            </Stack>
          ))}
        </Stack>

        <Box
          sx={{
            p: 3,
            borderRadius: 2,
            bgcolor: alpha(theme.palette.success.main, 0.05),
            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
          }}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Iconify
              icon="solar:star-bold"
              sx={{
                width: 24,
                height: 24,
                color: 'success.main',
              }}
            />
            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'success.main' }}>
                Hơn 5000+ khách hàng tin tưởng
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Từ startup đến tập đoàn lớn đều lựa chọn
              </Typography>
            </Box>
          </Stack>
        </Box>
      </Stack>
    </Card>
  );

  return (
    <Box
      id="contact-form"
      sx={{
        py: { xs: 8, md: 12 },
        background: `linear-gradient(135deg, ${varAlpha(theme.vars.palette.grey['500Channel'], 0.02)} 0%, transparent 100%)`,
      }}
    >
      <Container component={MotionViewport}>
        <Stack spacing={5}>
          {/* Section Header */}
          <m.div variants={varFade('inUp')}>
            <Stack spacing={3} sx={{ textAlign: 'center', maxWidth: 600, mx: 'auto' }}>
              <Typography variant="h2" sx={{ fontWeight: 700 }}>
                Báo giá
                <Box component="span" sx={{ color: 'primary.main' }}> túi vải in logo</Box>
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Form siêu nhanh - chỉ 5 thông tin cơ bản.
                Nhận tư vấn chi tiết qua điện thoại trong 30 phút.
              </Typography>
            </Stack>
          </m.div>

          {/* Form Content */}
          <Grid container spacing={4}>
            <Grid size={{ xs: 12, md: 7 }}>
              <m.div variants={varFade('inUp')}>
                {renderFormCard()}
              </m.div>
            </Grid>

            <Grid size={{ xs: 12, md: 5 }}>
              <m.div variants={varFade('inUp')}>
                {renderBenefitsCard()}
              </m.div>
            </Grid>
          </Grid>

          {/* Quick Contact Note */}
          <m.div variants={varFade('inUp')}>
            <Box
              sx={{
                textAlign: 'center',
                p: 3,
                borderRadius: 2,
                bgcolor: 'background.paper',
                boxShadow: '0 2px 12px rgba(0, 0, 0, 0.06)',
              }}
            >
              <Typography variant="body2" color="text.secondary">
                <Box component="span" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  Cần ngay?
                </Box>
                {' '}
                Nhắn Zalo
                <Box component="span" sx={{ fontWeight: 600 }}> 0938 069 715</Box>
                {' '}để được tư vấn trực tiếp ngay lập tức
              </Typography>
            </Box>
          </m.div>
        </Stack>
      </Container>
    </Box>
  );
}
