'use client';

import { useState } from 'react';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { LomaCTA } from '../home/<USER>';
import { LomaHero } from '../home/<USER>';
import { LomaProcess } from '../home/<USER>';
import { LomaProducts } from '../home/<USER>';
import { LomaTestimonials } from '../home/<USER>';

// New component for Video section
function LomaVideo() {
    return (
        <div style={{ textAlign: 'center', margin: '40px 0' }}>
            <h2>Giới thiệu về xưởng sản xuất</h2>
            <video width="600" controls>
                <source src="path/to/your/video.mp4" type="video/mp4" />
                Your browser does not support the video tag.
            </video>
            <p>Video tổng quan về xưởng, phù hợp với sản phẩm túi vải.</p>
        </div>
    );
}

// Updated LomaPromoView with promo focus
export function LomaPromoView() {
    const [open, setOpen] = useState(false);

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    return (
        <>
            <LomaHero />
            <LomaVideo /> {/* Added video section */}
            <LomaProcess />
            <LomaProducts />
            <LomaTestimonials />
            <Button variant="contained" onClick={handleClickOpen}>
                Nhận báo giá với ưu đãi 10%
            </Button>
            <LomaCTA />

            {/* MUI Dialog for consultation form */}
            <Dialog open={open} onClose={handleClose}>
                <DialogTitle>Form nhận báo giá chi tiết</DialogTitle> {/* Using DialogTitle for consistency */}
                <DialogContent>
                    <TextField autoFocus margin="dense" id="name" label="Tên" type="text" fullWidth />
                    <TextField margin="dense" id="email" label="Email" type="email" fullWidth />
                    <TextField margin="dense" id="quantity" label="Số lượng" type="number" fullWidth />
                    <p>Nhận ưu đãi 10% cho đơn hàng đầu tiên!</p>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>Hủy</Button>
                    <Button onClick={handleClose} variant="contained">Gửi</Button>
                </DialogActions>
            </Dialog>
        </>
    );
} 